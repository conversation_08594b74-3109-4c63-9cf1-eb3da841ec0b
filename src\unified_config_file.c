/**
 * @file unified_config_file.c
 * @brief Implementation of unified configuration file parser.
 */

#include "unified_config_file.h"
#include "native_config_parser.h"
#include "log.h"
#include <stdlib.h>
#include <string.h>

// Macro for error checking (reused from existing config files)
#define CHECK_CFG_ERROR(condition, message, ...)                              \
  if (!(condition)) {                                                          \
    log_error(message, ##__VA_ARGS__);                                         \
    status = 1;                                                                \
    goto clean_exit;                                                           \
  }

#define VERIFY_KEY(parser, section, key)                                       \
  CHECK_CFG_ERROR(config_parser_has_key(parser, section, key), "missing key %s in section %s", key, section)

int unified_config_file_parse(GlobalConfiguration *global_config,
                             TransportConfiguration *transport_config,
                             ApplicationConfiguration *application_config,
                             StoringConfiguration *storing_config,
                             const char *config_file_path) {
  int status = 0;
  ConfigParser parser;

  // Initialize the native parser
  if (config_parser_init(&parser) != 0) {
    log_error("Failed to initialize configuration parser");
    return 1;
  }

  // Parse the configuration file
  if (config_parser_parse_file(&parser, config_file_path) != 0) {
    log_error("Failed to parse configuration file %s", config_file_path);
    config_parser_cleanup(&parser);
    return 1;
  }

  // Process global configuration
  const char *log_level = config_parser_get_string(&parser, "global", CONFIG_LOG_LEVEL, NULL);
  if (log_level) {
    global_config->log_level = strdup(log_level);
    if (!global_config->log_level) {
      log_error("Failed to allocate memory for log_level");
      status = 1;
      goto clean_exit;
    }
  }

  global_config->daemonize = config_parser_get_bool(&parser, "global", CONFIG_DAEMONIZE, false);
  global_config->stop_after = config_parser_get_int(&parser, "global", CONFIG_STOP_AFTER, -1);

  const char *log_file = config_parser_get_string(&parser, "global", CONFIG_LOG_FILE, NULL);
  if (log_file) {
    global_config->log_file = strdup(log_file);
    if (!global_config->log_file) {
      log_error("Failed to allocate memory for log_file");
      status = 1;
      goto clean_exit;
    }
  }

  // Verify required global keys
  VERIFY_KEY(&parser, "global", CONFIG_LOG_LEVEL);

#ifdef TCP
  // Process TCP configuration
  const char *bind_ip = config_parser_get_string(&parser, "tcp", CONFIG_BIND_IP, NULL);
  if (bind_ip) {
    transport_config->bind_ip = strdup(bind_ip);
    if (!transport_config->bind_ip) {
      log_error("Failed to allocate memory for bind_ip");
      status = 1;
      goto clean_exit;
    }
  }

  transport_config->bind_port = config_parser_get_int(&parser, "tcp", CONFIG_BIND_PORT, -1);
  transport_config->recv_timeout = config_parser_get_int(&parser, "tcp", CONFIG_RECV_TIMEOUT, -1);

  // Verify required TCP keys
  VERIFY_KEY(&parser, "tcp", CONFIG_BIND_IP);
#endif

#ifdef RS232
  // Process RS232 configuration
  const char *port_name = config_parser_get_string(&parser, "rs232", CONFIG_PORT_NAME, NULL);
  if (port_name) {
    transport_config->port_name = strdup(port_name);
    if (!transport_config->port_name) {
      log_error("Failed to allocate memory for port_name");
      status = 1;
      goto clean_exit;
    }
  }

  transport_config->baud_rate = config_parser_get_int(&parser, "rs232", CONFIG_BAUD_RATE, -1);
  transport_config->bits = config_parser_get_int(&parser, "rs232", CONFIG_BITS, -1);
  transport_config->stop_bits = config_parser_get_int(&parser, "rs232", CONFIG_STOP_BITS, -1);

  // Process RS232 specific configurations
  const char *parity_str = config_parser_get_string(&parser, "rs232", CONFIG_PARITY, "NONE");
  if (strcmp(parity_str, "NONE") == 0) {
    transport_config->parity = SP_PARITY_NONE;
  } else if (strcmp(parity_str, "ODD") == 0) {
    transport_config->parity = SP_PARITY_ODD;
  } else if (strcmp(parity_str, "EVEN") == 0) {
    transport_config->parity = SP_PARITY_EVEN;
  } else {
    CHECK_CFG_ERROR(false, "invalid parity value: %s", parity_str);
  }

  const char *flow_control_str = config_parser_get_string(&parser, "rs232", CONFIG_FLOW_CONTROL, "NONE");
  if (strcmp(flow_control_str, "NONE") == 0) {
    transport_config->flow_control = SP_FLOWCONTROL_NONE;
  } else if (strcmp(flow_control_str, "XONXOFF") == 0) {
    transport_config->flow_control = SP_FLOWCONTROL_XONXOFF;
  } else if (strcmp(flow_control_str, "RTSCTS") == 0) {
    transport_config->flow_control = SP_FLOWCONTROL_RTSCTS;
  } else if (strcmp(flow_control_str, "DTRDSR") == 0) {
    transport_config->flow_control = SP_FLOWCONTROL_DTRDSR;
  } else {
    CHECK_CFG_ERROR(false, "invalid flow control value: %s", flow_control_str);
  }
#endif

#ifdef HL7
  // Process HL7 configuration
  const char *sending_application = config_parser_get_string(&parser, "hl7", CONFIG_SENDING_APPLICATION, NULL);
  if (sending_application) {
    application_config->sending_application = strdup(sending_application);
    if (!application_config->sending_application) {
      log_error("Failed to allocate memory for sending_application");
      status = 1;
      goto clean_exit;
    }
  }

  const char *sending_facility = config_parser_get_string(&parser, "hl7", CONFIG_SENDING_FACILITY, NULL);
  if (sending_facility) {
    application_config->sending_facility = strdup(sending_facility);
    if (!application_config->sending_facility) {
      log_error("Failed to allocate memory for sending_facility");
      status = 1;
      goto clean_exit;
    }
  }

  application_config->delay_before_ack = config_parser_get_int(&parser, "hl7", CONFIG_DELAY_BEFORE_ACK, -1);

  // Verify required HL7 keys
  VERIFY_KEY(&parser, "hl7", CONFIG_SENDING_APPLICATION);
  VERIFY_KEY(&parser, "hl7", CONFIG_SENDING_FACILITY);
#endif

#ifdef POCT
  // Process POCT configuration
  long response_timeout_ms = config_parser_get_int(&parser, "poct", CONFIG_RESPONSE_TIMEOUT, 0);
  application_config->response_timeout_ms = (size_t)response_timeout_ms;
#endif

#ifdef FILE_STORING
  // Process storing configuration
  const char *storing_directory = config_parser_get_string(&parser, "storing", CONFIG_STORING_DIRECTORY, NULL);
  if (storing_directory) {
    storing_config->storing_directory = strdup(storing_directory);
    if (!storing_config->storing_directory) {
      log_error("Failed to allocate memory for storing_directory");
      status = 1;
      goto clean_exit;
    }
  }

  // Verify required storing keys
  VERIFY_KEY(&parser, "storing", CONFIG_STORING_DIRECTORY);
#endif

clean_exit:
  config_parser_cleanup(&parser);
  return status;
}

int unified_config_file_validate(const char *config_file_path) {
  ConfigParser parser;

  // Initialize and parse the configuration file
  if (config_parser_init(&parser) != 0) {
    log_error("Failed to initialize configuration parser for validation");
    return 1;
  }

  if (config_parser_parse_file(&parser, config_file_path) != 0) {
    log_error("Configuration file validation failed: %s", config_file_path);
    config_parser_cleanup(&parser);
    return 1;
  }

  // Check for required global section
  if (!config_parser_has_key(&parser, "global", CONFIG_LOG_LEVEL)) {
    log_error("Missing required global configuration");
    config_parser_cleanup(&parser);
    return 1;
  }

  config_parser_cleanup(&parser);
  return 0;
}
