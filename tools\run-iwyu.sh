#!/bin/bash
# Usage: ./script.sh [iwyu|fix]
# 'iwyu' for running include-what-you-use, 'fix' for running iwyu-fix-includes

# Check if the argument is provided
if [ -z "$1" ]; then
    echo "Usage: $0 [see|fix]"
    exit 1
fi

# Get the mode: either 'iwyu' or 'fix'
MODE=$1

# Specify the base include directory
INCLUDE_DIR="-Iinclude"

# Add external directories
LIB_DIRS=$(find lib -mindepth 1 -maxdepth 1 -type d)

# Combine include paths
INCLUDE_PATHS="$INCLUDE_DIR"
for dir in $LIB_DIRS; do
    INCLUDE_PATHS="$INCLUDE_PATHS -I$dir/"
done

# Process each .c file
find src/ include/ lib/ -name "*.c" | while read -r file; do
    if [ "$MODE" == "see" ]; then
        # Run include-what-you-use
        echo "Running include-what-you-use on $file"
        include-what-you-use "$file" $INCLUDE_PATHS
    elif [ "$MODE" == "fix" ]; then
        # Run iwyu-fix-includes
        echo "Running iwyu-fix-includes on $file"
        include-what-you-use "$file" $INCLUDE_PATHS -Xiwyu --error_always 2>&1 | iwyu-fix-includes
    else
        echo "Unknown mode: $MODE. Use 'iwyu' or 'fix'."
        exit 1
    fi
done
