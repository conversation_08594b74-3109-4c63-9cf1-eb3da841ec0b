#pragma once

#include "common.h"
#include "specification.h"

#if HL7_VERSION == 260

extern const FieldInSegmentSpec MSA_Spec[8];

typedef enum {
  MSAAcknowledgmentCode = 0,
  MSAMessageControlID = 1,
  MSATextMessage = 2,
  MSAExpectedSequenceNumber = 3,
  MSADelayedAcknowledgmentType = 4,
  MSAErrorCondition = 5,
  MSAMessageWaitingNumber = 6,
  MSAMessageWaitingPriority = 7
} MSAFieldPosition;

#elif HL7_VERSION == 250

extern const FieldInSegmentSpec MSA_Spec[6];

typedef enum {
  MSAAcknowledgmentCode = 0,
  MSAMessageControlID = 1,
  MSATextMessage = 2,
  MSAExpectedSequenceNumber = 3,
  MSADelayedAcknowledgmentType = 4,
  MSAErrorCondition = 5
} MSAFieldPosition;

#elif HL7_VERSION == 240

extern const FieldInSegmentSpec MSA_Spec[6];

typedef enum {
  MSAAcknowledgmentCode = 0,
  MSAMessageControlID = 1,
  MSATextMessage = 2,
  MSAExpectedSequenceNumber = 3,
  MSADelayedAcknowledgmentType = 4,
  MSAErrorCondition = 5
} MSAFieldPosition;

#endif