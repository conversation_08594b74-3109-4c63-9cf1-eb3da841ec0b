#include "hd.h"

#if HL7_VERSION == 240

const SubFieldSpec HD_SPEC[3] = {
    {FieldTypeID, 0, OPTIONAL, TableType0300},
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeID, 0, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0301},
};

#elif HL7_VERSION == 250

const SubFieldSpec HD_SPEC[3] = {
    {FieldTypeID, 20, OPTIONAL, TableType0300},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 6, OPTIONAL, TableType0301},
};
#elif HL7_VERSION == 260

const SubFieldSpec HD_SPEC[3] = {
    {FieldTypeID, 20, OPTIONAL, TableType0300},
    {FieldTypeST, 999, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeID, 6, OPTIONAL, TableType0301},
};

#endif
