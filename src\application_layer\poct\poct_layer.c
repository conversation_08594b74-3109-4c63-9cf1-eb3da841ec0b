#include "poct_layer.h"

#include "application_to_storing.h"
#include "application_to_transport.h"
#include "binary_buffer.h"
#include "log.h"
#include "poct.h"
#include "poct_config.h"
#include "transport_to_application.h"
#include <libxml2/libxml/parser.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024 * 1000

// unlike input_output model, this module doesn't use
// dynamic allocationn
// this choice was taken considering embedded systems requirements
// static allocation allows compile-time memory allocation,
// helping to prevent running short of memory at runtime

static unsigned char buffer[BUFFER_SIZE];
BinaryBuffer *bb;

static ApplicationConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static PubSub *application2storing;
static POCTConversationHandler *pch;

#ifdef HB201DM

#include "hb201dm.h"

#else

#error "no driver specification for POCT"

#endif

int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing) {
  int status = 0;

  config = init_config;
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  application2storing = init_application2storing;

  poct_start();

  bb = bb_create(buffer, BUFFER_SIZE);
  if (!bb) {
    last_error_display(LOG_ERROR, "can't create binary buffer");
    status = 1;
    goto application_layer_init_clean;
  }

  if (add_subscriber(transport2application, application_layer_process_message,
                     NULL)) {
    last_error_display(LOG_ERROR, "can't subscribe");
    status = 1;
    goto application_layer_init_clean;
  }

  pch = poct_conversation_handler_create();
  if (!pch) {
    status = 1;
    goto application_layer_init_clean;
  }

application_layer_init_clean:
  if (status) {
    if (bb)
      bb_destruct(bb);

    if (pch)
      poct_conversation_handler_destruct(pch);
  }

  return status;
}

static void store_binary(const BinaryBufferDocument *doc, const char *ts,
                         const char *conversation_name) {
  size_t length;
  const unsigned char *data = bb_document_to_bytes(bb, doc, &length);
  BinaryFileData data_bin = {.bytes = data, .length = length};
  ApplicationToStoringData storing_data_bin = {.file_data = &data_bin,
                                               .file_type = FT_BINARY,
                                               .folder = conversation_name,
                                               .name = ts};
  publish_message(application2storing, &storing_data_bin);
}

static void store_textual(const BinaryBufferDocument *doc, const char *ts,
                          const char *conversation_name) {
  const char *str = bb_document_to_string(bb, doc);
  TextualFileData data_text = {.message = str};

  ApplicationToStoringData storing_data_txt = {.file_data = &data_text,
                                               .file_type = FT_TEXTUAL,
                                               .folder = conversation_name,
                                               .name = ts};
  publish_message(application2storing, &storing_data_txt);
}

static void send_response(POCTMessage **response) {
  if (!response) {
    log_error("can't send NULL response");
    return;
  }

  unsigned char *buffer = NULL;
  BinaryBuffer *bb_tmp = NULL;
  char *str;
  BinaryBufferDocument **docs;
  size_t count = 0;

  for (POCTMessage **response_ptr = response; *response_ptr; response_ptr++) {
    if (buffer)
      free(buffer);
    if (bb_tmp)
      free(bb_tmp);

    str = poct_message_to_string(*response_ptr);

    if (!str)
      continue;

    size_t buffer_len = strlen(str) + 4;
    LOG_CHECK_MALLOC(buffer, malloc(buffer_len));

    bb_tmp = bb_from_string(str, buffer, buffer_len);
    free(str);
    if (!bb_tmp) {
      last_error_display(LOG_ERROR,
                         "can't create binary buffer for response sending");
      goto send_response_clean;
    }

    docs = bb_to_documents_try(bb_tmp);
    if (!docs[0] || docs[1]) {
      log_error("one document should have been created");
      for (BinaryBufferDocument **ptr = docs; *ptr; ptr++)
        free(*ptr);
      free(docs);

      continue;
    }

    size_t length;
    const unsigned char *bytes = bb_document_to_bytes(bb_tmp, docs[0], &length);
    ApplicationToTransportData data = {.bytes = bytes, .length = length};
    publish_message(application2transport, &data);
    free(docs[0]);
    free(docs);

    count++;
  }

send_response_clean:
  if (bb_tmp)
    bb_destruct(bb_tmp);
  if (buffer)
    free(buffer);
}

void process_message(const BinaryBufferDocument *doc) {
  POCTMessage *message = NULL;
  POCTMessageHandlingResult *handling_result = NULL;
  const char *message_name = poct_message_name(pch);
  const char *conversation_name = poct_conversation_name(pch);

  // binary storage
  store_binary(doc, message_name, conversation_name);

  // textual storage
  store_textual(doc, message_name, conversation_name);

  const char *str = bb_document_to_string(bb, doc);

  // parsing
  log_start(LOG_DEBUG, "parsing document...");
  message = poct_message_from_string(str, &poct_specification);
  if (!message) {
    last_error_display(LOG_WARN, "can't create message");
    goto process_message_clean;
  }
  log_end(LOG_DEBUG, "done.");

  // validation
  log_start(LOG_DEBUG, "validating document...");
  handling_result = pch->handler(message, pch->context);
  if (!handling_result)
    goto process_message_clean;

  if (handling_result->message_is_valid) {
    log_end(LOG_DEBUG, "document is valid");

    if (handling_result->response) {
      log_start(LOG_DEBUG,
                "waiting %zu milliseconds before sending response...",
                config->response_timeout_ms);
      usleep(config->response_timeout_ms * 1000);
      log_end(LOG_DEBUG, "done.");
      send_response(handling_result->response);
    } else
      log_debug("no response needed");
  } else {
    log_warn("document is invalid");
  }

process_message_clean:
  poct_message_destruct(message);
  poct_handling_result_destruct(handling_result);
  bb_clean(bb);
}

void application_layer_process_message(void *void_context,
                                       const void *void_data) {
  (void)void_context;
  const TransportToApplicationData *data =
      (TransportToApplicationData *)void_data;

  // invalid length
  if (data->length == 0) {
    log_warn("received zero-length message");
    return;
  }

  bb_append(bb, data->bytes, data->length);
  BinaryBufferDocument **docs = bb_to_documents_try(bb);

  // TODO: poct handler when binary buffer is dropped
  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++) {
      process_message(*ptr);
      free(*ptr);
    }

    free(docs);
  } else
    last_error_display(LOG_WARN, "can't read binary buffer");
}

void application_layer_end() {
  poct_conversation_handler_destruct(pch);
  bb_destruct(bb);
  poct_end();
}