
#include "cli.h"
#include "daemon.h"
#include "unified_config_file.h"
#include "log.h"
#include <errno.h>
#include <libgen.h>
#include <signal.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// transport layer
#ifdef TCP
#include "tcp_layer.h"
#elif RS232
#include "rs232_layer.h"
#endif

// application layer
#ifdef HL7
#include "hl7_layer.h"
#elif POCT
#include "poct_layer.h"
#endif

// storing layer
#ifdef FILE_STORING
#include "file_storing_layer.h"
#endif

#define BUFFER_CAPACITY 1024 * 1000

#define REQUIRE_CONDITION(condition_fn, ...)                                   \
  if (!(condition_fn)) {                                                       \
    last_error_display(LOG_FATAL, __VA_ARGS__);                                \
    exit(1);                                                                   \
  }

// TODO: test RS232

// TODO: what happens if messages directories get deleted after init ? or
// logging file ?

// TODO: remove checks of memory allocations (LOG_MALLOC_CHECK handles throws
// signal if malloc error already)

static CLIArguments cli;
static GlobalConfiguration global_config;
static TransportConfiguration transport_config;
static ApplicationConfiguration application_config;
static StoringConfiguration storing_config;
static PubSub transport2application;
static PubSub application2transport;
static PubSub application2storing;
static char current_working_directory[PATH_MAX_LENGTH];
static FILE *log_file;

void init() {
  log_start(LOG_DEBUG, "initialising...");
  const time_t t = time(NULL);
  srand((unsigned int)t); // not optimal, good-enough security

  log_set_level(LOG_DEBUG);
  global_configuration_init(&global_config);
  transport_configuration_init(&transport_config);
  application_configuration_init(&application_config);
  storing_configuration_init(&storing_config);
  pubsub_init(&transport2application);
  pubsub_init(&application2transport);
  pubsub_init(&application2storing);
  log_end(LOG_DEBUG, "done.");
}

void read_config(const int argc, const char *argv[]) {
  char *program_copy;
  LOG_CHECK_MALLOC(program_copy, strdup(argv[0]));

  char *program = basename(program_copy);
  // cli and config parsing
  log_start(LOG_DEBUG, "parsing configurations...");
  REQUIRE_CONDITION(!cli_parse(&cli, argc, &argv[0], program),
                    "couldn't parse CLI, aborting");
  REQUIRE_CONDITION(
      !unified_config_file_parse(&global_config, &transport_config,
                                &application_config, &storing_config,
                                cli.config_file),
      "couldn't parse unified config file, aborting");
  free(program_copy);
  cli_destruct(&cli);

  log_end(LOG_DEBUG, "done.");
}

void log_config() {
  char *str;
  str = global_configuration_to_string(&global_config);
  log_info("global config : %s", str);
  free(str);

  str = transport_configuration_to_string(&transport_config);
  log_info("transport config : %s", str);
  free(str);

  str = application_configuration_to_string(&application_config);
  log_info("application config : %s", str);
  free(str);

  str = storing_configuration_to_string(&storing_config);
  log_info("storing config : %s", str);
  free(str);
}

void handle_daemon_mode() {
  REQUIRE_CONDITION(getcwd(current_working_directory,
                           sizeof(current_working_directory)) != NULL,
                    "getcwd() error");
  log_info("current working directory : %s", current_working_directory);

  if (global_config.daemonize) {
    log_start(LOG_INFO, "entering daemon mode...");
    daemonize(current_working_directory, NULL);
    log_end(LOG_INFO, "done.");
  }
}

void setup_logging() {
  log_start(LOG_DEBUG, "setting up logging...");
  const int log_level = string_to_log_level(global_config.log_level);
  REQUIRE_CONDITION(log_level != -1, "invalid log level, aborting");
  log_set_level(log_level);
  log_file = fopen(global_config.log_file, "w");
  REQUIRE_CONDITION(log_file != NULL,
                    "can't open log file %s, aborting (errno:%d - %s)",
                    global_config.log_file, errno, strerror(errno));
  log_add_fp(log_file, LOG_TRACE);
  log_end(LOG_DEBUG, "done.");
  log_info("log level : %s", global_config.log_level);
  log_info("logs will be stored in : %s", global_config.log_file);
}

void init_layers() {
  // transport
  log_start(LOG_DEBUG, "setting up transport layer...");
  REQUIRE_CONDITION(!transport_layer_init(&transport_config,
                                          &transport2application,
                                          &application2transport),
                    "transport layer could not be initialized, aborting");
  log_end(LOG_DEBUG, "done.");

  // application
  log_start(LOG_DEBUG, "setting up application layer...");
  REQUIRE_CONDITION(
      !application_layer_init(&application_config, &transport2application,
                              &application2transport, &application2storing),
      "application layer could not be initialized, aborting");
  log_end(LOG_DEBUG, "done.");

  // storing
  log_start(LOG_DEBUG, "setting up storing layer...");
  REQUIRE_CONDITION(!storing_layer_init(&storing_config, &application2storing),
                    "storing layer could not be initialized, aborting");
  log_end(LOG_DEBUG, "done.");
}

void end() {
  log_start(LOG_INFO, "ending program...");
  transport_layer_end();
  application_layer_end();
  global_configuration_destruct(&global_config);
  transport_configuration_destruct(&transport_config);
  application_configuration_destruct(&application_config);
  storing_configuration_destruct(&storing_config);
  log_end(LOG_INFO, "done.");
  log_debug("shutting down logging");
  if (log_file)
    fclose(log_file);
}

void handle_sigterm(int sig) {
  (void)sig;
  log_info("received SIGTERM, shutting down");
  end();
  exit(0);
}

void handle_abort(int sig) {
  (void)sig;
  last_error_display(LOG_FATAL, "abort() was called, shutting down");
  end();
  exit(0);
}

void setup_signal_handlers() {
  signal(SIGTERM, handle_sigterm);
  signal(SIGINT, handle_sigterm);
  signal(SIGABRT, handle_abort);
}

int main(const int argc, const char *argv[]) {
  setup_signal_handlers();
  init();
  read_config(argc, argv);
  handle_daemon_mode();
  setup_logging();
  log_config();
  init_layers();

  log_info("starting driver");
  // entry point
  transport_layer_read_publish_loop();

  end();

  return EXIT_SUCCESS;
}
