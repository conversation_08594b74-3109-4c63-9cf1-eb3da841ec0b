#include "obx.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec OBX_Spec[19] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"value type", FieldTypeID, 2, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TableType0125},
    {"observation identifier", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"obersvation sub-id", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation value", FieldTypeVARIES, 65536, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"reference range", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"abnormal flags", FieldTypeIS, 5, OPTIONAL, 5, TableType0078},
    {"probability", FieldTypeNM, 5, O<PERSON><PERSON>ON<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"nature of abnormal test", FieldTypeID, 2, OPTIONAL, REPEATABLE_INFINITE, TableType0080},
    {"observation result status", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0085},
    {"date last observation normal value", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"user defined access checks", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"date/time of the observation", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"producer's ID", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"responsible observer", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"observation method", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"equipment instance identifier", FieldTypeEI, 22, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"date/time of the analysis", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec OBX_Spec[19] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"value type", FieldTypeID, 2, OPTIONAL, NOT_REPEATABLE, TableType0125},
    {"observation identifier", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"obersvation sub-id", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation value", FieldTypeVARIES, 99999, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"reference range", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"abnormal flags", FieldTypeIS, 5, OPTIONAL, 5, TableType0078},
    {"probability", FieldTypeNM, 5, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"nature of abnormal test", FieldTypeID, 2, OPTIONAL, REPEATABLE_INFINITE, TableType0080},
    {"observation result status", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0085},
    {"date last observation normal value", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"user defined access checks", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"date/time of the observation", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"producer's ID", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"responsible observer", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"observation method", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"equipment instance identifier", FieldTypeEI, 22, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"date/time of the analysis", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#elif HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec OBX_Spec[25] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"value type", FieldTypeID, 3, OPTIONAL, NOT_REPEATABLE, TableType0125},
    {"observation identifier", FieldTypeCE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"obersvation sub-id", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation value", FieldTypeVARIES, 99999, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"units", FieldTypeCE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"reference range", FieldTypeST, 60, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"abnormal flags", FieldTypeIS, 5, OPTIONAL, 5, TableType0078},
    {"probability", FieldTypeNM, 5, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"nature of abnormal test", FieldTypeID, 2, OPTIONAL, REPEATABLE_INFINITE, TableType0080},
    {"observation result status", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0085},
    {"date last observation normal value", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"user defined access checks", FieldTypeST, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"date/time of the observation", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"producer's ID", FieldTypeCWE, 705, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"responsible observer", FieldTypeXCN, 3220, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"observation method", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"equipment instance identifier", FieldTypeEI, 427, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"date/time of the analysis", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"observation site", FieldTypeCWE, 705, OPTIONAL, REPEATABLE_INFINITE, TableType0163},
    {"observation instance identifier", FieldTypeEI, 427, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"mood code", FieldTypeCNE, 705, OPTIONAL, NOT_REPEATABLE, TableType0725},
    {"performing organization name", FieldTypeXON, 570, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"performing organization address", FieldTypeXAD, 2915, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"performing organization medical director", FieldTypeXCN, 3220, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#endif