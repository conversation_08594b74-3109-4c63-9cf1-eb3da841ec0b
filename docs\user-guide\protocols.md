# Protocoles supportés

Les drivers Synlab supportent plusieurs protocoles de communication pour s'adapter à différents types d'automates.

## Architecture en couches

Le système utilise une architecture modulaire en 3 couches :

```
┌─────────────────┐
│   APPLICATION   │  HL7, POCT
├─────────────────┤
│    TRANSPORT    │  TCP/IP, RS232
├─────────────────┤
│    STOCKAGE     │  Fichiers
└─────────────────┘
```

Cette architecture permet de **combiner n'importe quel protocole de transport avec n'importe quel protocole d'application**.

## Protocoles de transport

### TCP/IP

**Usage :** Automates connectés au réseau Ethernet

**Configuration :**
```ini
[tcp]
bind-ip = "*************"
bind-port = 1234
recv-timeout = 120
```

**Avantages :**
- ✅ Connexion réseau stable
- ✅ Débit élevé
- ✅ Connexions multiples possibles
- ✅ Surveillance réseau facile

**Inconvénients :**
- ❌ Nécessite une infrastructure réseau
- ❌ Configuration IP requise

### RS232 (Série)

**Usage :** Automates avec port série uniquement

**Configuration :**
```ini
[rs232]
port-name = "/dev/ttyUSB0"
baud-rate = 9600
bits = 8
parity = "NONE"
stop-bits = 1
flow-control = "NONE"
```

**Avantages :**
- ✅ Connexion directe simple
- ✅ Pas de configuration réseau
- ✅ Fiable sur courte distance
- ✅ Standard industriel

**Inconvénients :**
- ❌ Débit limité
- ❌ Distance limitée
- ❌ Une seule connexion par port

## Protocoles d'application

### HL7 (Health Level 7)

**Usage :** Standard hospitalier pour l'échange de données médicales

**Versions supportées :**
- HL7 v2.4
- HL7 v2.5  
- HL7 v2.6

**Configuration :**
```ini
[hl7]
version = "2.6"
sending-application = "LAB"
sending-facility = "SYNLAB"
```

**Structure des messages :**
```
MSH|^~\&|LAB|SYNLAB|LIS|HOSPITAL|20231201120000||ORU^R01|123456|P|2.6
PID|1||12345^^^SYNLAB||DOE^JOHN||19800101|M
OBR|1||LAB123|GLU^Glucose|||20231201120000
OBX|1|NM|GLU^Glucose||120|mg/dL|70-110|H|||F
```

**Types de messages supportés :**
- **ORU^R01** : Résultats d'observation
- **ADT^A01** : Admission patient
- **QRY^A19** : Requête patient

### POCT (Point of Care Testing)

**Usage :** Appareils de diagnostic au point de soin

**Configuration :**
```ini
[poct]
device-id = "POCT001"
```

**Caractéristiques :**
- Messages plus simples que HL7
- Orienté résultats immédiats
- Format compact

## Combinaisons courantes

### TCP/IP + HL7

**Cas d'usage :** Automates de laboratoire modernes

```ini
[tcp]
bind-ip = "*************"
bind-port = 1234

[hl7]
version = "2.6"
sending-application = "F200"
```

**Exemples d'automates :**
- Analyseurs de chimie clinique
- Automates d'hématologie
- Systèmes de microbiologie

### RS232 + POCT

**Cas d'usage :** Appareils portables ou anciens

```ini
[rs232]
port-name = "/dev/ttyUSB0"
baud-rate = 9600

[poct]
device-id = "GLUCOSE001"
```

**Exemples d'automates :**
- Glucomètres
- Analyseurs de gaz du sang
- Tests rapides

### TCP/IP + POCT

**Cas d'usage :** Appareils POCT connectés

```ini
[tcp]
bind-ip = "0.0.0.0"
bind-port = 2000

[poct]
device-id = "POCT_NET001"
```

## Stockage des données

Tous les protocoles utilisent le même système de stockage :

```ini
[storing]
output-directory = "./data"
create-subdirs = true
```

**Organisation des fichiers :**
```
data/
├── 2023/
│   ├── 12/
│   │   ├── 01/
│   │   │   ├── message_001.hl7
│   │   │   ├── message_002.hl7
│   │   │   └── ...
```

## Ajout de nouveaux protocoles

L'architecture modulaire permet d'ajouter facilement de nouveaux protocoles :

### Nouveau protocole de transport

1. Implémenter l'interface transport
2. Ajouter la configuration
3. Compiler avec le nouveau protocole

### Nouveau protocole d'application

1. Implémenter l'interface application
2. Définir le format des messages
3. Ajouter les règles de validation

## Dépannage par protocole

### Problèmes TCP/IP

```bash
# Vérifier l'écoute
netstat -tlnp | grep 1234

# Tester la connexion
telnet ************* 1234

# Vérifier le firewall
sudo iptables -L | grep 1234
```

### Problèmes RS232

```bash
# Vérifier le port
ls -la /dev/ttyUSB*

# Tester les permissions
sudo chmod 666 /dev/ttyUSB0

# Vérifier la configuration
stty -F /dev/ttyUSB0
```

### Problèmes HL7

```bash
# Valider la structure du message
grep "MSH|" message.hl7

# Vérifier les segments obligatoires
grep -E "(MSH|PID|OBR|OBX)" message.hl7
```

## Prochaines étapes

- [**Configuration détaillée**](configuration.md) - Paramètres spécifiques
- [**Exemples concrets**](examples.md) - Configurations réelles
- [**Dépannage**](troubleshooting.md) - Résolution de problèmes
