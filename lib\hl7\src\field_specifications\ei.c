#include "ei.h"

#if HL7_VERSION == 240

const SubFieldSpec EI_SPEC[4] = {
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeIS, 0, OPTION<PERSON>, TableType0300},
    {FieldTypeST, 0, <PERSON>P<PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeID, 0, OP<PERSON><PERSON><PERSON>, TableType0301},
};

#elif HL7_VERSION == 250 || HL7_VERSION == 260

const SubFieldSpec EI_SPEC[4] = {
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeIS, 20, OPTIONAL, TableType0363},
    {FieldTypeST, 199, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 6, OPTIONAL, TableType0301},
};

#endif
