# Premier lancement

Ce guide vous accompagne pour votre premier test des drivers d'automates Synlab.

## Vérification préalable

Avant de commencer, assurez-vous que :

- ✅ La compilation s'est bien déroulée
- ✅ Les binaires sont présents dans `build/bin/`
- ✅ Vous avez les droits d'accès aux ports série (si nécessaire)

```bash
# Vérifier la présence des drivers
ls -la build/bin/

# Tester l'aide d'un driver
./build/bin/f200 --help
```

## Test rapide avec la configuration d'exemple

### 1. Utiliser la configuration de test

Le projet inclut une configuration d'exemple prête à l'emploi :

<augment_code_snippet path="tests/sample_configs/unified.config" mode="EXCERPT">
````ini
# Configuration unifiée pour les drivers automatisés
global {
  log-level = "DEBUG"
  daemonize = false
  log-file = "./main.log"
}

tcp {
  bind-ip = "127.0.0.1"
  bind-port = 1234
  recv-timeout = 120
}
````
</augment_code_snippet>

### 2. Lancer le driver en mode test

```bash
# Aller dans le répertoire build
cd build

# Lancer le driver F200 avec la configuration d'exemple
./bin/f200 -c ../tests/sample_configs/unified.config
```

### 3. Vérifier le fonctionnement

Le driver devrait :

1. **Démarrer sans erreur** et afficher des logs
2. **Écouter sur le port TCP** configuré (1234)
3. **Créer un fichier de log** (`main.log`)

```bash
# Dans un autre terminal, vérifier l'écoute TCP
netstat -tlnp | grep 1234

# Vérifier les logs
tail -f main.log
```

## Test de connexion TCP

### Simuler une connexion

```bash
# Tester la connexion TCP avec telnet
telnet 127.0.0.1 1234

# Ou avec netcat
nc 127.0.0.1 1234
```

### Envoyer un message de test HL7

Exemple de message HL7 minimal :

```
MSH|^~\&|LAB|SYNLAB|LIS|HOSPITAL|20231201120000||ORU^R01|123456|P|2.6
OBX|1|ST|GLU^Glucose||120|mg/dL|70-110|H|||F
```

## Test avec port série (RS232)

### Configuration RS232

Créez un fichier de test `test_rs232.config` :

```ini
[global]
log-level = "DEBUG"
log-file = "./rs232_test.log"

[rs232]
port-name = "/dev/ttyUSB0"  # Adapter selon votre port
baud-rate = 9600
bits = 8
parity = "NONE"
stop-bits = 1
flow-control = "NONE"

[poct]
device-id = "TEST001"

[storing]
output-directory = "./data_test"
```

### Lancer le test RS232

```bash
# Créer le répertoire de données
mkdir -p data_test

# Lancer le driver
./bin/[votre_driver] -c test_rs232.config
```

## Vérification des résultats

### Logs

Vérifiez que les logs contiennent :

```bash
# Rechercher les messages importants
grep -E "(Started|Listening|Connected|Error)" main.log

# Vérifier les erreurs
grep -i error main.log
```

### Données stockées

Si des messages sont reçus, vérifiez le stockage :

```bash
# Lister les fichiers créés
find ./data_test -type f -name "*.txt" -o -name "*.hl7"

# Examiner le contenu
cat ./data_test/[fichier_créé]
```

## Résolution de problèmes courants

### Le driver ne démarre pas

```bash
# Vérifier les dépendances
ldd ./bin/f200

# Vérifier la configuration
./bin/f200 -c ../tests/sample_configs/unified.config --validate-config
```

### Erreur "Permission denied" sur le port série

```bash
# Vérifier les permissions
ls -la /dev/ttyUSB0

# Ajouter l'utilisateur au groupe dialout
sudo usermod -a -G dialout $USER
newgrp dialout
```

### Erreur "Address already in use" (TCP)

```bash
# Vérifier qui utilise le port
sudo netstat -tlnp | grep 1234

# Changer le port dans la configuration
# ou arrêter le processus qui utilise le port
```

### Pas de connexion TCP

```bash
# Vérifier le firewall
sudo iptables -L | grep 1234

# Tester avec une autre IP
# Changer bind-ip de "127.0.0.1" à "0.0.0.0"
```

## Arrêt propre

Pour arrêter le driver :

```bash
# Ctrl+C dans le terminal du driver
# Ou envoyer un signal SIGTERM
kill -TERM [PID_DU_DRIVER]
```

## Prochaines étapes

Une fois le test réussi :

1. [**Configuration personnalisée**](configuration.md) - Adapter à votre environnement
2. [**Utilisation avancée**](usage.md) - Fonctionnalités complètes
3. [**Exemples**](examples.md) - Cas d'usage concrets

!!! success "Test réussi !"
    Si le driver démarre, écoute sur le port configuré et traite les messages, votre installation est opérationnelle !

!!! tip "Mode debug"
    Gardez `log-level = "DEBUG"` pendant les tests pour avoir un maximum d'informations dans les logs.
