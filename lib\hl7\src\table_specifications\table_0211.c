#include "table_0211.h"

#if HL7_VERSION == 240

const char *Table0211Values[] = {
    "8859/1",   "8859/2",    "8859/3",   "8859/4",  "8859/5",
    "8859/6",   "8859/7",    "8859/8",   "8859/9",  "ASCII",
    "ISO IR14", "ISO IR159", "ISO IR87", "UNICODE",
};

const HL7Table Table0211 = {.table_name = "alternate character sets",
                            .valid_values = Table0211Values,
                            .value_count = 14};

#elif HL7_VERSION == 250

const char *Table0211Values[] = {
    "8859/1",        "8859/2",   "8859/3",         "8859/4",
    "8859/5",        "8859/6",   "8859/7",         "8859/8",
    "8859/9",        "ASCII",    "BIG-5",          "CNS 11643-1992",
    "GB 18030-2000", "ISO IR14", "ISO IR159",      "ISO IR87",
    "KS X 1001",     "UNICODE",  "UNICODE UTF-16", "UNICODE UTF-32",
    "UNICODE UTF-8",
};

const HL7Table Table0211 = {.table_name = "alternate character sets",
                            .valid_values = Table0211Values,
                            .value_count = 21};

#elif HL7_VERSION == 260

const char *Table0211Values[] = {
    "8859/1",         "8859/15",       "8859/2",   "8859/3",
    "8859/4",         "8859/5",        "8859/6",   "8859/7",
    "8859/8",         "8859/9",        "ASCII",    "BIG-5",
    "CNS 11643-1992", "GB 18030-2000", "ISO IR14", "ISO IR159",
    "ISO IR87",       "KS X 1001",     "UNICODE",  "UNICODE UTF-16",
    "UNICODE UTF-32", "UNICODE UTF-8",
};

const HL7Table Table0211 = {.table_name = "alternate character sets",
                            .valid_values = Table0211Values,
                            .value_count = 22};

#endif
