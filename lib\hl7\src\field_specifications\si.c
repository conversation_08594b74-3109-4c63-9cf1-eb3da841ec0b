#include "si.h"

#include "specification.h"
#include "st.h"
#include <stdlib.h>

#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260

void validate_SI(const char *field, HL7ParsingErrorsHelper *helper) {
  validate_ST(field, helper);

  int c = atoi(field);

  if (c < 0 || c > 9999) {
    hl7_errors_helper_add(
        helper, "invalid value : expected in range [0, 9999], got %d", c);
  }
}

#endif