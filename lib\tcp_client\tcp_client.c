#include "tcp_client.h"

#include "log.h"
#include <arpa/inet.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>

static int create_socket(int timeout_reception) {
  const int sock = socket(AF_INET, SOCK_STREAM, 0);
  if (sock == -1) {
    last_error_set("can't create TCP socket (errno:%d - %s)", errno,
                   strerror(errno));
    return -1;
  }

  const struct timeval tv = {.tv_sec = timeout_reception, .tv_usec = 0};

  setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (const char *)&tv, sizeof tv);
  return sock;
}

void tcp_client_destruct(TcpClient *client) {
  if (!client)
    return;

  if (client->descriptor)
    close(client->descriptor);

  free(client);
}

void tcp_client_terminate(TcpClient *client) {
  fflush(stdout);
  shutdown(client->descriptor, SHUT_WR);
  shutdown(client->descriptor, SHUT_RD);
  tcp_client_destruct(client);
}

int tcp_client_connect(TcpClient *client) {
  if (connect(client->descriptor, (struct sockaddr *)&client->server_address,
              sizeof(struct sockaddr_in))) {
    last_error_set("can't open connection (errno:%d - %s)", errno,
                   strerror(errno));
    return 1;
  }

  return 0;
}

ssize_t tcp_client_receive(TcpClient *client, char *buffer, int buffer_length,
                           int *disconnected) {
  *disconnected = 0;
  const ssize_t received_bytes_count =
      recv(client->descriptor, buffer, (size_t)buffer_length, 0);

  if (received_bytes_count < 0) {
    last_error_set("an error happened during message reception "
                   "(errno:%d - %s)",
                   errno, strerror(errno));
    if (errno == ECONNRESET)
      *disconnected = 1;

    return -1;
  }

  return received_bytes_count;
}

int tcp_client_send(TcpClient *client, const char *buffer, int buffer_length) {
  log_start(LOG_DEBUG, "sending %d bytes...", buffer_length);
  const ssize_t nombre =
      send(client->descriptor, buffer, (size_t)buffer_length, 0);
  if (nombre < 0) {
    last_error_set("an error happened during message sending (errno:%d - %s)",
                   errno, strerror(errno));
    return 1;
  }
  if (nombre != buffer_length) {
    last_error_set("not all bytes have been sent (%ld/%ul)", nombre,
                   buffer_length);
    return 1;
  }

  log_end(LOG_DEBUG, "done.");

  return 0;
}

int tcp_client_reconnect(TcpClient *client) {
  close(client->descriptor);
  int attempts = 0;
  int status = 1;
  // -1 means infinitly reconnecting trials
  while ((attempts < client->max_reconnect_attemps) ||
         (-1 == client->max_reconnect_attemps)) {

    tcp_client_terminate(client);
    const int sock = create_socket(client->reception_timeout);
    if (-1 == sock)
      break;

    client->descriptor = sock;
    attempts += 1;
    if (connect(client->descriptor, (struct sockaddr *)&client->server_address,
                sizeof(client->server_address)) != 0) {
      log_debug("reconnection attempt failed");

      sleep((unsigned int)client->deay_between_reconnections);
      continue;
    }
    status = 0;
    break;
  }

  if ((client->max_reconnect_attemps == attempts) && (status == 1))
    last_error_set("maximum number of reconnection attempts reached (%d)",
                   client->max_reconnect_attemps);

  return status;
}

TcpClient *tcp_client_create(const char *ip, unsigned short port,
                             int max_reconnect_attemps,
                             int deay_between_reconnections,
                             int reception_timeout) {
  const int sock = create_socket(reception_timeout);
  if (sock == -1)
    return NULL;

  TcpClient *client;
  LOG_CHECK_MALLOC(client, malloc(sizeof(TcpClient)));

  client->max_reconnect_attemps = max_reconnect_attemps;
  client->deay_between_reconnections = deay_between_reconnections;
  client->descriptor = sock;
  client->reception_timeout = reception_timeout;

  bzero(&client->server_address, sizeof(struct sockaddr_in));
  client->server_address.sin_family = AF_INET;
  client->server_address.sin_addr.s_addr = inet_addr(ip);
  client->server_address.sin_port = htons(port);

  return client;
}
