/**
 * @file tcp_client.h
 * @brief Simple TCP client.
 *
 * Synopsis:
 *
 *      int status;
 *      ssize_t bytes_received;
 *
 *      // Client creation.
 *      TcpClient *client_tcp = tcp_client_creer(...);
 *      if (NULL == client_tcp) { ... }
 *
 *      // Connection to the server.
 *      status = client_tcp->connect(client_tcp);
 *      if (0 != status) { ... }
 *
 *      // Receiving data from the server.
 *      bytes_received = client_tcp->receive(client_tcp...);
 *      if (-1 == bytes_received) { ... }
 *
 *      // Sending data to the server.
 *      status = client_tcp->send(client_tcp...);
 *      if (0 != status) { ... }
 *
 *      // Reconnecting to the server if needed.
 *      status = client_tcp->reconnect(client_tcp);
 *      if (0 != status) { ... }
 *
 *      // Closing the connection.
 *      client_tcp->terminate(client_tcp);
 *
 *      // Freeing the memory allocated for the client.
 *      free(client_tcp);
 */

#pragma once

#include "log.h"
#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/types.h>
#include <time.h>

/**
 * @brief TCP client.
 */
typedef struct StructTcpClient {
  /**
   * @brief Address of the server to which a connection is opened.
   */
  struct sockaddr_in server_address;
  /**
   * @brief  Socket number used by the client.
   */
  int descriptor;
  /**
   * @brief  Maximum number of reconnection attempts.
   */
  int max_reconnect_attemps;
  /**
   * @brief  Number of seconds to wait between two reconnection attempts.
   */
  int deay_between_reconnections;
  /**
   * @brief Number of seconds for the receive timeout.
   */
  int reception_timeout;
} TcpClient;

/**
 * @brief Creates a TCP client.
 *
 * @param ip Address of a string representing the IP address
 * of the server to which a connection will be opened.
 * @param port TCP port number used by the server to which a
 * connection will be opened.
 * @param max_reconnect_attemps Maximum number of reconnection attempts to
 * the server (in case the connection to the server is lost).
 * @param deay_between_reconnections Number of seconds to wait between two
 * reconnection attempts.
 *
 * @return
 * - Success: the address of a new TCP client
 * - Error: NULL
 */
TcpClient *tcp_client_create(const char *ip, unsigned short port,
                             int max_reconnect_attemps,
                             int deay_between_reconnections,
                             int reception_timeout);

/**
 * @brief Connects the client (to the server whose "coordinates" were
 * provided when the client was created).
 *
 * @param client
 * @return
 * - Success: 0
 * - Error: 1
 */
int tcp_client_connect(TcpClient *client);

/**
 * @brief Reconnects the client (to the server whose "coordinates" were
 * provided when the client was created).
 *
 * @param client
 *
 * @return
 * - Success: 0
 * - Error: 1
 */
int tcp_client_reconnect(TcpClient *client_tcp);

/**
 * @brief Receive a message.
 *
 * @param client
 * @param buffer Address of the memory area to use for storing the received
 * message.
 * @param buffer_length Number of bytes allocated for the `buffer`
 * memory area.
 * @param disconnected Address of an indicator to check whether an error
 * resulted from a connection interruption. 0: the error did not result from
 * a connection interruption. 1: the error resulted from a connection
 * interruption.
 *
 * @return
 * - Success: the number of bytes received
 * - Error: -1
 */
ssize_t tcp_client_receive(TcpClient *client, char *buffer, int buffer_length,
                           int *disconnected);

/**
 * @brief Sending data.
 *
 * @param client
 * @param buffer Address of the memory area to use for storing the
 * data to be sent.
 * @param buffer_length Number of bytes in the memory area used to
 * store the data to be sent.
 *
 * @return
 * - Success: the number of bytes received
 *- Error: -1
 */
int tcp_client_send(TcpClient *client, const char *buffer, int buffer_length);

/**
 * @brief Releases the resources allocated to the client if it is connected
 * (to a server).
 *
 * @param client
 */
void tcp_client_terminate(TcpClient *client);

/**
 * @brief Frees the resources allocated to the client if it is not
 * connected (to a server).
 *
 * @param client
 */
void tcp_client_destruct(TcpClient *client);