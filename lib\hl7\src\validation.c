#include "validation.h"

#include "ce.h"
#include "cne.h"
#include "cq.h"
#include "cwe.h"
#include "cx.h"
#include "dtm.h"
#include "ei.h"
#include "ft.h"
#include "hd.h"
#include "id.h"
#include "is.h"
#include "log.h"
#include "msg.h"
#include "nm.h"
#include "pt.h"
#include "si.h"
#include "sps.h"
#include "st.h"
#include "ts.h"
#include "varies.h"
#include "vid.h"
#include "xcn.h"
#include "xpn.h"

#include "eqp.h"
#include "equ.h"
#include "msa.h"
#include "msh.h"
#include "nte.h"
#include "obr.h"
#include "obx.h"
#include "orc.h"
#include "pid.h"
#include "sac.h"
#include "sid.h"

#include "table_0001.h"
#include "table_0003.h"
#include "table_0004.h"
#include "table_0007.h"
#include "table_0008.h"
#include "table_0009.h"
#include "table_0023.h"
#include "table_0065.h"
#include "table_0069.h"
#include "table_0070.h"
#include "table_0076.h"
#include "table_0078.h"
#include "table_0085.h"
#include "table_0092.h"
#include "table_0102.h"
#include "table_0103.h"
#include "table_0104.h"
#include "table_0112.h"
#include "table_0116.h"
#include "table_0119.h"
#include "table_0123.h"
#include "table_0125.h"
#include "table_0155.h"
#include "table_0163.h"
#include "table_0177.h"
#include "table_0200.h"
#include "table_0211.h"
#include "table_0301.h"
#include "table_0326.h"
#include "table_0354.h"
#include "table_0357.h"
#include "table_0360.h"
#include "table_0369.h"
#include "table_0396.h"
#include "table_0450.h"
#include "table_0482.h"
#include "table_0483.h"
#include "table_0725.h"

#include <stdlib.h>

// ##########################################
// Table validation
// ##########################################

// clang-format off
static const HL7Table *hl7_tables[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [TableType0001] = &Table0001,
    [TableType0003] = &Table0003,
    [TableType0008] = &Table0008,
    [TableType0065] = &Table0065,
    [TableType0076] = &Table0076,
    [TableType0078] = &Table0078,
    [TableType0085] = &Table0085,
    [TableType0103] = &Table0103,
    [TableType0104] = &Table0104,
    [TableType0125] = &Table0125,
    [TableType0155] = &Table0155,
    [TableType0163] = &Table0163,
    [TableType0200] = &Table0200,
    [TableType0211] = &Table0211,
    [TableType0301] = &Table0301,
    [TableType0354] = &Table0354,
    [TableType0357] = &Table0357,
    [TableType0360] = &Table0360,
    [TableType0396] = &Table0396,
#endif
#if HL7_VERSION == 260
    [TableType0725] = &Table0725,
#endif
#if HL7_VERSION == 250
    [TableType0004] = &Table0004,
    [TableType0007] = &Table0007,
    [TableType0009] = &Table0009,
    [TableType0023] = &Table0023,
    [TableType0069] = &Table0069,
    [TableType0092] = &Table0092,
    [TableType0112] = &Table0112,
    [TableType0116] = &Table0116,
    [TableType0123] = &Table0123,
    [TableType0177] = &Table0177,
    [TableType0326] = &Table0326,
    [TableType0482] = &Table0482,
    [TableType0483] = &Table0483,
#endif
#if HL7_VERSION == 240
    [TableType0070] = &Table0070,
    [TableType0102] = &Table0102,
    [TableType0119] = &Table0119,
    [TableType0369] = &Table0369,
    [TableType0450] = &Table0450,
#endif
    [TableTypesCount] = NULL,
};

static const bool is_table_user_defined[] = {
    [TableType0010] = true,
    [TableType0018] = true,
    [TableType0021] = true,
    [TableType0032] = true,
    [TableType0044] = true,
    [TableType0045] = true,
    [TableType0046] = true,
    [TableType0073] = true,
    [TableType0087] = true,
    [TableType0099] = true,
    [TableType0110] = true,
    [TableType0111] = true,
    [TableType0114] = true,
    [TableType0115] = true,
    [TableType0117] = true,
    [TableType0300] = true,
    [TableType0361] = true,
    [TableType0362] = true,
    [TableType0363] = true,
    [TableType0385] = true,
    [TableType0552] = true,
    [TableTypesCount] = false,
};
// clang-format on

// custom comparison function that handles escaped wildcard characters and
// trailing wildcards
static bool is_valid_match(const char *valid_value, const char *field_value) {
  while (*valid_value && *field_value) {
    if (*valid_value == '\\') {
      // wildcard: match any character in field_value
      valid_value++;
      field_value++;
    } else {
      // regular character comparison
      if (*valid_value != *field_value)
        return false;
      valid_value++;
      field_value++;
    }
  }

  while (*valid_value == '\\')
    valid_value++; // skip trailing wildcards

  // the valid_value can end in wildcards, but field_value must be fully
  // consumed
  return *field_value == '\0';
}

void hl7_table_lookup(TableType table_type, const Field *field,
                      HL7ParsingErrorsHelper *helper) {
  if (field->type != FIELD_SIMPLE) {
    hl7_errors_helper_add(
        helper,
        "field was expected to be in table type %s, but has subcomponents",
        hl7_table_type2str(table_type));
    return;
  }

  if (is_table_user_defined[table_type])
    return;

  const HL7Table *table = hl7_tables[table_type];
  if (!table) {
    hl7_errors_helper_add(helper,
                          "no validator avaible for table %s (unhandled)",
                          hl7_table_type2str(table_type));
    return;
  }

  for (size_t j = 0; j < table->value_count; j++)
    if (is_valid_match(table->valid_values[j], field->value))
      return; // value is valid according to the table

  hl7_errors_helper_add(helper, "value \"%s\" not found in table %s (\"%s\")",
                        field->value, hl7_table_type2str(table_type),
                        table->table_name);
}

// ##########################################
// Field validation
// ##########################################

typedef void (*FieldValidator)(const char *value,
                               HL7ParsingErrorsHelper *helper);

// FIXME: unhandled xpn and sps despite being specified

// A table that maps field types to their validators
// clang-format off
static const FieldValidator simple_field_validators[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [FieldTypeST] = validate_ST,
    [FieldTypeIS] = validate_IS,
    [FieldTypeID] = validate_ID,
    [FieldTypeDTM] = validate_DTM,
    [FieldTypeSI] = validate_SI,
    [FieldTypeVARIES] = validate_VARIES,
    [FieldTypeNM] = validate_NM,
    [FieldTypeFT] = validate_FT,
#endif
    [FieldTypesCount] = NULL
};

static const ComposedFieldSpec *composed_field_specs[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [FieldTypeHD] = &HD_SPEC,
    [FieldTypeMSG] = &MSG_SPEC,
    [FieldTypePT] = &PT_SPEC,
    [FieldTypeCE] = &CE_SPEC,
    [FieldTypeCWE] = &CWE_SPEC,
    [FieldTypeVID] = &VID_SPEC,
    [FieldTypeEI] = &EI_SPEC,
    [FieldTypeCNE] = &CNE_SPEC,
    [FieldTypeCQ] = &CQ_SPEC,
    [FieldTypeCX] = &CX_SPEC,
    [FieldTypeXCN] = &XCN_SPEC,
    [FieldTypeXPN] = &XPN_SPEC,
    [FieldTypeTS] = &TS_SPEC,
#endif
#if HL7_VERSION == 240
    [FieldTypeSPS] = &SPS_SPEC,
#endif
    [FieldTypesCount] = NULL
};

static const size_t composed_field_specs_lengths[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [FieldTypeHD] = sizeof(HD_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeMSG] = sizeof(MSG_SPEC) / sizeof(SubFieldSpec),
    [FieldTypePT] = sizeof(PT_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeCE] = sizeof(CE_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeCWE] = sizeof(CWE_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeVID] = sizeof(VID_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeEI] = sizeof(EI_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeCNE] = sizeof(CNE_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeCQ] = sizeof(CQ_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeCX] = sizeof(CX_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeXCN] = sizeof(XCN_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeXPN] = sizeof(XPN_SPEC) / sizeof(SubFieldSpec),
    [FieldTypeTS] = sizeof(TS_SPEC) / sizeof(SubFieldSpec),
#endif
#if HL7_VERSION == 240
    [FieldTypeSPS] = sizeof(SPS_SPEC) / sizeof(SubFieldSpec),
#endif
    [FieldTypesCount] = 0
};
// clang-format on

static void field_is_valid(const Field *field, FieldType field_type,
                           bool optionnal, TableType table_type,
                           HL7ParsingErrorsHelper *helper) {
  size_t field_length = hl7_field_length(field);

  // variable type : it can be anything
  if (field_type == FieldTypeVARIES)
    goto field_is_valid_clean;

  if (field_length == 0) {
    if (!optionnal)
      hl7_errors_helper_add(helper, "field is not optionnal (type %s)",
                            hl7_field_type2str(field_type));
    goto field_is_valid_clean;
  }

  if (table_type != TABLE_NONE)
    hl7_table_lookup(table_type, field, helper);

  FieldValidator validator = simple_field_validators[field_type];
  const ComposedFieldSpec *spec = composed_field_specs[field_type];
  const size_t spec_length = composed_field_specs_lengths[field_type];

  if (!validator && !spec) {
    hl7_errors_helper_add(
        helper, "no validator or specification available (unhandled)");
    goto field_is_valid_clean;
  }

  // simple field
  if (validator) {
    if (field->type != FIELD_SIMPLE) {
      hl7_errors_helper_add(
          helper, "expected simple field (type %s), got field with components",
          hl7_field_type2str(field_type));
      goto field_is_valid_clean;
    }

    validator(field->value, helper);
    goto field_is_valid_clean;
  }

  // composed field
  size_t min_subfields_count = 0; // optionnal ending subfields can be omitted

  for (size_t i = 0; i < spec_length; i++)
    if (!(*spec)[i].optional)
      min_subfields_count = i + 1;

  // only one subfield was specified
  if (field->type == FIELD_SIMPLE) {
    if (min_subfields_count <= 1) {
      const SubFieldSpec child_spec = (*spec)[0];
      helper->current_position.field_type = child_spec.type;
      field_is_valid(field, child_spec.type, child_spec.optional,
                     child_spec.table, helper);
      return;
    } else {
      hl7_errors_helper_add(helper,
                            "expected field with at least %zu components (type "
                            "%s), got simple field",
                            min_subfields_count,
                            hl7_field_type2str(field_type));
      goto field_is_valid_clean;
    }
  }

  if (min_subfields_count > field->children_count ||
      spec_length < field->children_count) {
    hl7_errors_helper_add(
        helper, "expected field count in range [%zu, %zu], got %zu (type %s)",
        min_subfields_count, spec_length, field->children_count,
        hl7_field_type2str(field_type));
    goto field_is_valid_clean;
  }

  for (size_t i = 0; i < field->children_count; i++) {
    const Field *child = hl7_field_get_child(field, i);
    const SubFieldSpec child_spec = (*spec)[i];
    const size_t child_length = hl7_field_length(child);

    if (child_spec.max_length != 0 && child_length > child_spec.max_length) {
      hl7_errors_helper_add(
          helper, "subfield is too long : got %zu characters, maximum is %zu",
          child_length, child_spec.max_length);
    }
    helper->current_position.field_type = field_type;
    field_is_valid(child, child_spec.type, child_spec.optional,
                   child_spec.table, helper);

    // delimiter
    if (i < field->children_count - 1)
      helper->current_position.character_index++;
  }

  return;

field_is_valid_clean:
  helper->current_position.character_index += field_length;
}

void hl7_field_is_valid(const Field *field, const FieldInSegmentSpec *spec,
                        HL7ParsingErrorsHelper *helper) {
  size_t field_length = hl7_field_length(field);

  // check the length of the field
  if (field_length == 0) {
    if (!spec->optional) {
      hl7_errors_helper_add(helper, "required field missing");
      goto hl7_field_is_valid_clean;
    }

    if (spec->max_length > 0 && field_length > (size_t)spec->max_length) {
      hl7_errors_helper_add(
          helper, "maximum length exceeded: expected max %zu, got %zu",
          spec->max_length, field_length);
      goto hl7_field_is_valid_clean;
    }

    if (spec->repeatability == 0) {
      hl7_errors_helper_add(helper, "bad value for repeatability in "
                                    "specification : 0. -1 means infinite, "
                                    "positive values define an upper limit");
      goto hl7_field_is_valid_clean;
    }
  }

  if (field->type == FIELD_REPEATED) {
    if (spec->repeatability == 0) {
      hl7_errors_helper_add(helper, "repeated field is not allowed here");
      goto hl7_field_is_valid_clean;
    }

    if (spec->repeatability != -1 &&
        field->children_count > (size_t)spec->repeatability) {
      hl7_errors_helper_add(
          helper, "field repeated too many times : %zu repetitions, max %zu",
          field->children_count, spec->repeatability);
      goto hl7_field_is_valid_clean;
    }

    // validate subfields
    for (size_t i = 0; i < field->children_count; i++) {
      const Field *child = hl7_field_get_child(field, i);
      field_is_valid(child, spec->type, spec->optional, spec->table_type,
                     helper);
      // delimiter
      if (i < field->children_count - 1)
        helper->current_position.character_index++;
    }
  } else {
    field_is_valid(field, spec->type, spec->optional, spec->table_type, helper);
  }

  return;

hl7_field_is_valid_clean:
  helper->current_position.character_index += field_length;
}

// ##########################################
// Segment validation
// ##########################################

// A table that maps segment types to their specifications
// clang-format off
static const SegmentSpecification *segment_specifications[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [SegmentTypeMSH] = &MSH_Spec,
    [SegmentTypePID] = &PID_Spec,
    [SegmentTypeOBR] = &OBR_Spec,
    [SegmentTypeOBX] = &OBX_Spec,
    [SegmentTypeMSA] = &MSA_Spec,
    [SegmentTypeNTE] = &NTE_Spec,
#endif
#if HL7_VERSION == 240
    [SegmentTypeSAC] = &SAC_Spec,
    [SegmentTypeSID] = &SID_Spec,
    [SegmentTypeEQP] = &EQP_Spec,
    [SegmentTypeEQU] = &EQU_Spec,
    [SegmentTypeORC] = &ORC_Spec,
#endif
    [SegmentTypesCount] = NULL};
// clang-format on

// Array that stores the lengths of each specification
static const size_t segment_spec_sizes[] = {
#if HL7_VERSION == 240 || HL7_VERSION == 250 || HL7_VERSION == 260
    [SegmentTypeMSH] = sizeof(MSH_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypePID] = sizeof(PID_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeOBR] = sizeof(OBR_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeOBX] = sizeof(OBX_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeMSA] = sizeof(MSA_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeNTE] = sizeof(NTE_Spec) / sizeof(FieldInSegmentSpec),
#endif
#if HL7_VERSION == 240
    [SegmentTypeSAC] = sizeof(SAC_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeSID] = sizeof(SID_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeEQP] = sizeof(EQU_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeEQU] = sizeof(EQP_Spec) / sizeof(FieldInSegmentSpec),
    [SegmentTypeORC] = sizeof(ORC_Spec) / sizeof(FieldInSegmentSpec),
#endif
    [SegmentTypesCount] = 0};

void hl7_segment_is_valid(const Segment *segment,
                          HL7ParsingErrorsHelper *helper) {
  if (!segment || !segment->fields) {
    hl7_errors_helper_add(helper,
                          "segment is null, can't determine its validity");
    return;
  }

  SegmentType type = segment->type;

  helper->current_position.character_index = 0;

  // is segment type handled ?
  const SegmentSpecification *spec = segment_specifications[type];
  if (!spec) {
    hl7_errors_helper_add(helper, "no specification available (unhandled)");
    return;
  }

  helper->current_position.segment_spec = spec;
  // sgement name + first delimiter
  helper->current_position.character_index += 4;

  size_t spec_length = segment_spec_sizes[type];
  size_t field_count = hl7_segment_get_fields_count(segment);

  // check if there are fewer fields than required (non-optional fields
  // missing)
  for (size_t i = field_count; i < spec_length; i++) {
    FieldInSegmentSpec field_spec = (*spec)[i];
    if (!field_spec.optional) {
      helper->current_position.field_index = i;
      helper->current_position.field_type = field_spec.type;

      hl7_errors_helper_add(
          helper,
          "not enough fields : required fields were omitted in segment end",
          field_spec.name, i, hl7_segment_type2str(type));
      return;
    }
  }

  // validate each field
  for (size_t i = 0; i < field_count; i++) {
    // don't validate delimiter in MSH
    if (i == 0 && segment->type == SegmentTypeMSH)
      continue;

    const Field *field = hl7_segment_get_field(segment, i);
    FieldInSegmentSpec field_spec = (*spec)[i];
    helper->current_position.field_index = i;
    helper->current_position.field_type = field_spec.type;
    hl7_field_is_valid(field, &field_spec, helper);
    helper->current_position.character_index++; // delimiter
  }

  helper->current_position.field_index = 0;
  helper->current_position.field_type = FieldTypeUnknown;
}

// ##########################################
// Message validation
// ##########################################

char *hl7_message_is_valid(const Message *message) {
  HL7ParsingErrorsHelper *helper = hl7_errors_helper_create(message);

  // is there a MSH segment ?
  const Segment *segment =
      hl7_message_get_segment_by_type(message, SegmentTypeMSH);
  if (!segment)
    hl7_errors_helper_add(helper, "no MSH segment");

  // are segments valid ?
  for (size_t i = 0; i < hl7_message_get_segments_count(message); i++) {
    const Segment *segment = hl7_message_get_segment_by_index(message, i);
    helper->current_position.segment_index = i;
    helper->current_position.segment_type = segment->type;

    hl7_segment_is_valid(segment, helper);
  }

  char *error_str = hl7_errors2str(helper);
  hl7_errors_helper_destruct(helper);

  return error_str;
}
