#pragma once

#include "common.h"
#include "specification.h"

#if HL7_VERSION == 260

extern const FieldInSegmentSpec MSH_Spec[25];

typedef enum {
  MSHFieldSeparator = 0,
  MSHEncodingCharacters = 1,
  MSHSendingApplication = 2,
  MSHSendingFacility = 3,
  MSHReceivingApplication = 4,
  MSHReceivingFacility = 5,
  MSHDateTimeOfMessage = 6,
  MSHSecurity = 7,
  MSHMessageType = 8,
  MSHMessageControlID = 9,
  MSHProcessingID = 10,
  MSHVersionID = 11,
  MSHSequenceNumber = 12,
  MSHContinuationPointer = 13,
  MSHAcceptAcknowledgmentType = 14,
  MSHApplicationAcknowledgmentType = 15,
  MSHCountryCode = 16,
  MSHCharacterSet = 17,
  MSHPrincipalLanguageOfMessage = 18,
  MS<PERSON>lternateCharacterSetHandlingScheme = 19,
  MSHMessageProfileIdentifier = 20,
  MSHSendingResponsibleOrganization = 21,
  MSHReceivingResponsibleOrganization = 21,
  MSHSendingNetworkAddress = 22,
  MSHReceivingNetworkAddress = 23,
} MSHFieldPosition;

#elif HL7_VERSION == 250

extern const FieldInSegmentSpec MSH_Spec[21];

typedef enum {
  MSHFieldSeparator = 0,
  MSHEncodingCharacters = 1,
  MSHSendingApplication = 2,
  MSHSendingFacility = 3,
  MSHReceivingApplication = 4,
  MSHReceivingFacility = 5,
  MSHDateTimeOfMessage = 6,
  MSHSecurity = 7,
  MSHMessageType = 8,
  MSHMessageControlID = 9,
  MSHProcessingID = 10,
  MSHVersionID = 11,
  MSHSequenceNumber = 12,
  MSHContinuationPointer = 13,
  MSHAcceptAcknowledgmentType = 14,
  MSHApplicationAcknowledgmentType = 15,
  MSHCountryCode = 16,
  MSHCharacterSet = 17,
  MSHPrincipalLanguageOfMessage = 18,
  MSHAlternateCharacterSetHandlingScheme = 19,
  MSHMessageProfileIdentifier = 20
} MSHFieldPosition;

#elif HL7_VERSION == 240

extern const FieldInSegmentSpec MSH_Spec[21];

typedef enum {
  MSHFieldSeparator = 0,
  MSHEncodingCharacters = 1,
  MSHSendingApplication = 2,
  MSHSendingFacility = 3,
  MSHReceivingApplication = 4,
  MSHReceivingFacility = 5,
  MSHDateTimeOfMessage = 6,
  MSHSecurity = 7,
  MSHMessageType = 8,
  MSHMessageControlID = 9,
  MSHProcessingID = 10,
  MSHVersionID = 11,
  MSHSequenceNumber = 12,
  MSHContinuationPointer = 13,
  MSHAcceptAcknowledgmentType = 14,
  MSHApplicationAcknowledgmentType = 15,
  MSHCountryCode = 16,
  MSHCharacterSet = 17,
  MSHPrincipalLanguageOfMessage = 18,
  MSHAlternateCharacterSetHandlingScheme = 19,
  MSHConformanceStatementID = 20
} MSHFieldPosition;

#endif