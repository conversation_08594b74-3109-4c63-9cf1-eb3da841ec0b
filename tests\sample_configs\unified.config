# Configuration unifiée pour les drivers automatisés
# Ce fichier remplace les anciens fichiers séparés :
# - global.config, tcp.config, rs232.config, hl7.config, poct.config, storing.config

global {
  # Configuration globale de l'application
  log-level = "DEBUG"
  daemonize = false
  log-file = "./main.log"
  # stop-after = -1  # -1 = run forever, utilisé pour les tests uniquement
}

tcp {
  # Configuration de la couche transport TCP
  bind-ip = "127.0.0.1"
  bind-port = 1234
  recv-timeout = 120
}

rs232 {
  # Configuration de la couche transport RS232
  port-name = "/tmp/ttyS1"
  baud-rate = 9600
  bits = 8
  parity = "NONE"
  stop-bits = 1
  flow-control = "NONE"
}

hl7 {
  # Configuration de la couche application HL7
  sending-application = "test-app-1234"
  sending-facility = "test-facility-1234"
  delay-before-ack = 1
}

poct {
  # Configuration de la couche application POCT
  response-timeout-ms = 2000
}

storing {
  # Configuration de la couche de stockage
  storing-dir = "."
}
