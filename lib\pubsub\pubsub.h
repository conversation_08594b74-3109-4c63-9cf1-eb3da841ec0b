#include <arpa/inet.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <unistd.h>

#define MAX_SUBSCRIBERS 10

#pragma once

/// @brief Callback function type for subscribers
///
/// @param context An arbitrary-typed object, furnished during subscription.
/// This allows the callback to preserver state between calls. The callback is
/// fully responsible of this object.
/// @param data An arbitrary-type object, furnished by the publisher. The
/// publisher and subscriber should agree beforehand on the data structure that
/// is being passed through this call.
typedef void (*MessageSubscriber)(void *context, const void *data);

/// @brief Structure representing a subscriber
typedef struct {
  /// @brief  Callback function, called each time a message is published
  MessageSubscriber subscriber;
  /// @brief State associated with the subscriber
  /// This allows the subscribers to keep a state
  /// between calls.
  void *context;
} Subscriber;

/// @brief Structure representing a publish/subcribe system
typedef struct {
  Subscriber subscribers[MAX_SUBSCRIBERS];
  int count;
} PubSub;

/// @brief Allocates needed ressources for a PubSub structure.
///
/// @param pubsub
void pubsub_init(PubSub *pubsub);

/// @brief Adds a callback on a PubSub.
///
/// The function will be called every time a message is published on the PubSub.
///
/// @param pubsub
/// @param subscriber a callback function, as defined by `MessageSubscriber`
/// @param context an arbitrary-type object the callback will receive in
/// addition to the data every time it is called (see `MessageSubscriber`)
///
/// @return
/// - Success : 0
/// - Error : 1
int add_subscriber(PubSub *pubsub, MessageSubscriber subscriber, void *context);

/// @brief Publish a message on a PubSub.
///
/// @param pubsub
/// @param data An arbitrary-type object. The publisher and subscriber should
/// agree beforehand on the data structure that is being passed through this
/// call.
void publish_message(PubSub *pubsub, const void *data);
