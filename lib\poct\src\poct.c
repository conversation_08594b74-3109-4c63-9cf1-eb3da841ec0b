#include <poct.h>

#include "log.h"
#include <stdlib.h>
#include <string.h>
#include <time.h>

#define DATE_LENGTH 40

void poct_start() { LIBXML_TEST_VERSION xmlInitParser(); }

const char *poct_generate_iso8601_timestamp() {
  static char buffer[DATE_LENGTH];
  time_t now = time(NULL);
  struct tm local_tm;
  localtime_r(&now, &local_tm);
  strftime(buffer, DATE_LENGTH, "%Y-%m-%dT%H:%M:%S", &local_tm);

  // Calculate the timezone offset (in minutes)
  struct tm gmt_tm;
  gmtime_r(&now, &gmt_tm); // Get the equivalent UTC time (GMT)

  // offset in minutes
  long timezone_offset = difftime(mktime(&local_tm), mktime(&gmt_tm)) / 60;

  // Convert timezone offset to hours and minutes
  int hours_offset = timezone_offset / 60;
  int minutes_offset = timezone_offset % 60;

  // Append the timezone offset to the timestamp string
  snprintf(buffer + strlen(buffer), DATE_LENGTH - strlen(buffer), "%+03d:%02d",
           hours_offset, minutes_offset);
  return buffer;
}

void poct_handling_result_destruct(POCTMessageHandlingResult *result) {
  if (!result)
    return;

  if (result->response) {
    for (POCTMessage **ptr = result->response; *ptr; ptr++)
      poct_message_destruct(*ptr);

    free(result->response);
  }

  free(result);
}

void poct_end() { xmlCleanupParser(); }