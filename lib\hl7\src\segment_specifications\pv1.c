#include "obr.h"

#if HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec PV1_Spec[52] = {
    {"set ID - PV1", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient class", FieldTypeIS, 1, REQUIRED, NOT_REPEATABLE, TableType0004},
    {"assigned patient location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"admission type", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0007},
    {"preadmit number", FieldTypeCX, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"prior patient location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"attending doctor", FieldTypeXCN, 250, OPTIONAL, REPEA<PERSON>BLE_INFINITE, TableType0010},
    {"referring doctor", FieldTypeXCN, 250, <PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON><PERSON><PERSON><PERSON>_INFINITE, TableType0010},
    {"consulting doctor", FieldType<PERSON>C<PERSON>, 250, <PERSON>P<PERSON><PERSON><PERSON>, R<PERSON>EATABLE_INFINITE, TableType0010},
    {"hospital service", FieldTypeIS, 3, OPTIONAL, NOT_REPEATABLE, TableType0069},
    {"temporary location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"preadmit test indicator", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0087},
    {"re-admission indicator", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0092},
    {"admit source", FieldTypeIS, 6, OPTIONAL, NOT_REPEATABLE, TableType0023},
    {"ambulatory status", FieldTypeIS, 2, OPTIONAL, REPEATABLE_INFINITE, TableType0009},
    {"VIP indicator", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0099},
    {"admitting doctor", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0010},
    {"patient type", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0018},
    {"visit number", FieldTypeCX, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"financial class", FieldTypeFC, 50, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"charge price indicator", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0032},
    {"courtesy code", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0045},
    {"credit rating", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0046},
    {"contract code", FieldTypeIS, 2, OPTIONAL, REPEATABLE_INFINITE, TableType0044},
    {"contract effective date", FieldTypeDT, 8, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"contract amount", FieldTypeNM, 12, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"contract period", FieldTypeNM, 3, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"interest code", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0073},
    {"transfer to bad debt code", FieldTypeIS, 4, OPTIONAL, NOT_REPEATABLE, TableType0110},
    {"transfer to bad debt date", FieldTypeDT, 8, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"bad debt agency code", FieldTypeIS, 10, OPTIONAL, NOT_REPEATABLE, TableType0021},
    {"bad debt transfer amount", FieldTypeNM, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"bad debt recovery amount", FieldTypeNM, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"delete account indicator", FieldTypeIS, 1, OPTIONAL, NOT_REPEATABLE, TableType0111},
    {"delete account date", FieldTypeDT, 8, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"dicharge disposition", FieldTypeIS, 3, OPTIONAL, NOT_REPEATABLE, TableType0112},
    {"discharged to location", FieldTypeDLD, 47, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"diet type", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0114},
    {"servicing facility", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0115},
    {"bed status", FieldTypeIS, 2, OPTIONAL, NOT_REPEATABLE, TableType0116},
    {"account status", FieldTypeIS, 1, OPTIONAL, NOT_REPEATABLE, TableType0117},
    {"pending location", FieldTypePL, 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"prior temporary location", FieldTypePL, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"admit date/time", FieldTypeTS, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"discharge date/time", FieldTypeTS, 26, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"current patient balance", FieldTypeNM, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"total charges", FieldTypeNM, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"total adjustements", FieldTypeNM, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"total payments", FieldTypeNM, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"alternate visite ID", FieldTypeCX, 12, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"visit indicator", FieldTypeIS, 1, OPTIONAL, NOT_REPEATABLE, TableType0326},
    {"other healthcare provider", FieldTypeXCN, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0010},
};
// clang-format on

#endif
