# Guide Utilisateur - Driver Universel Synlab

Bienvenue dans le guide d'utilisation du driver Universel Synlab. Ce guide vous accompagnera dans l'installation, la configuration et l'utilisation des drivers pour connecter vos automates de laboratoire.

## Qu'est-ce que le driver Universel Synlab ?

Le driver Synlab permettent de connecter différents automates de laboratoire à votre système d'information. Ils supportent plusieurs protocoles de communication et peuvent s'adapter à différents types d'équipements.

### Protocoles supportés

- **Transport** : TCP/IP, RS232
- **Application** : HL7 (versions 2.4, 2.5, 2.6), POCT1
- **Stockage** : Système de fichiers avec organisation par dossiers

## Démarrage rapide

!!! tip "TLDR - Lancement rapide"
    ```bash
    ./mon_driver -c ma_configuration.config
    ```

### Exemple concret
```bash
# Pour un automate F200 en TCP/IP avec HL7
./bin/f200 -c ./configs/f200_tcp_hl7.config
```

## Architecture simplifiée

Le système fonctionne en 3 couches indépendantes :

1. **Transport** : Comment communiquer avec l'automate (TCP/IP ou RS232)
2. **Application** : Quel protocole utiliser (HL7 ou POCT)
3. **Stockage** : Où sauvegarder les données reçues

Cette architecture modulaire permet de combiner n'importe quel transport avec n'importe quel protocole.

## Prochaines étapes

1. [**Installation**](installation.md) - Installer les prérequis et compiler
2. [**Configuration**](configuration.md) - Créer votre fichier de configuration
3. [**Premier lancement**](quickstart.md) - Tester votre installation
4. [**Utilisation**](usage.md) - Utiliser les drivers au quotidien

## Besoin d'aide ?

- Consultez la [FAQ](faq.md) pour les questions courantes
- Voir [Résolution de problèmes](troubleshooting.md) en cas de difficultés
- Référez-vous aux [Exemples](examples.md) pour des cas d'usage concrets

!!! warning "Important"
    Ce guide est destiné aux **utilisateurs finaux**. Si vous êtes développeur et souhaitez modifier ou étendre les drivers, consultez la documentation développeur générée par Doxygen.
