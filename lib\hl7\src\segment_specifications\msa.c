#include "msa.h"

#if HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec MSA_Spec[8] = {
    {"acknowledgment code", FieldTypeID, 2, REQUIRED, NOT_REPEATABLE, TableType0008},
    {"message control ID", FieldTypeST, 199, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"text message", FieldTypeST, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"expected sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"delayed acknowledgment type", FieldTypeST, 0, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"error condition", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0357},
    {"message waiting number", FieldTypeNM, 5, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"message waiting priority", FieldTypeID, 1, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TableType0520},
};
// clang-format on

#elif HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec MSA_Spec[6] = {
    {"acknowledgment code", FieldTypeID, 2, REQUIRED, NOT_REPEATABLE, TableType0008},
    {"message control ID", FieldTypeST, 20, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"text message", FieldTypeST, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"expected sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"delayed acknowledgment type", FieldTypeST, 0, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"error condition", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0357},
};
// clang-format on

#elif HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec MSA_Spec[6] = {
    {"acknowledgment code", FieldTypeID, 2, REQUIRED, NOT_REPEATABLE, TableType0008},
    {"message control ID", FieldTypeST, 20, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"text message", FieldTypeST, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"expected sequence number", FieldTypeNM, 15, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"delayed acknowledgment type", FieldTypeID, 1, OPTIONAL, NOT_REPEATABLE, TableType0102},
    {"error condition", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0357},
};
// clang-format on

#endif