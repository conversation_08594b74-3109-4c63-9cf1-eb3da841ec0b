# Référence complète de configuration

Cette page documente tous les paramètres de configuration disponibles pour les drivers d'automates Synlab.

## Structure générale

Le fichier de configuration utilise le format INI avec sections :

```ini
# Commentaires commencent par #

[section]
parametre = "valeur"
nombre = 123
booleen = true
```

## Section [global]

Configuration générale de l'application.

### log-level

**Type :** String  
**Valeurs :** `"DEBUG"`, `"INFO"`, `"WARN"`, `"ERROR"`  
**Défaut :** `"INFO"`  
**Description :** Niveau de verbosité des logs

```ini
[global]
log-level = "DEBUG"    # Maximum de détails
log-level = "INFO"     # Informations générales (recommandé)
log-level = "WARN"     # Avertissements uniquement
log-level = "ERROR"    # Erreurs uniquement
```

### daemonize

**Type :** Boolean  
**Valeurs :** `true`, `false`  
**Défaut :** `false`  
**Description :** Exécuter en arrière-plan

```ini
[global]
daemonize = false    # Mode interactif (logs visibles)
daemonize = true     # Mode démon (arrière-plan)
```

### log-file

**Type :** String  
**Défaut :** `"./driver.log"`  
**Description :** Chemin du fichier de log

```ini
[global]
log-file = "./logs/driver.log"           # Chemin relatif
log-file = "/var/log/synlab/driver.log"  # Chemin absolu
```

### stop-after

**Type :** Integer  
**Défaut :** `-1` (jamais)  
**Description :** Arrêter après N messages (pour tests)

```ini
[global]
stop-after = -1     # Fonctionnement continu
stop-after = 10     # Arrêt après 10 messages
stop-after = 100    # Arrêt après 100 messages
```

## Section [tcp]

Configuration du transport TCP/IP.

### bind-ip

**Type :** String  
**Défaut :** `"127.0.0.1"`  
**Description :** Adresse IP d'écoute

```ini
[tcp]
bind-ip = "127.0.0.1"      # Local uniquement
bind-ip = "0.0.0.0"        # Toutes les interfaces
bind-ip = "*************"  # Interface spécifique
```

### bind-port

**Type :** Integer  
**Plage :** 1-65535  
**Défaut :** `1234`  
**Description :** Port TCP d'écoute

```ini
[tcp]
bind-port = 1234    # Port standard
bind-port = 8080    # Port alternatif
bind-port = 3000    # Port personnalisé
```

### recv-timeout

**Type :** Integer  
**Unité :** Secondes  
**Défaut :** `120`  
**Description :** Timeout de réception

```ini
[tcp]
recv-timeout = 30     # 30 secondes (tests)
recv-timeout = 120    # 2 minutes (standard)
recv-timeout = 300    # 5 minutes (gros volumes)
recv-timeout = 600    # 10 minutes (très patient)
```

## Section [rs232]

Configuration du transport série.

### port-name

**Type :** String  
**Description :** Nom du port série

```ini
[rs232]
# Linux
port-name = "/dev/ttyS0"     # Port série intégré
port-name = "/dev/ttyUSB0"   # Adaptateur USB-série
port-name = "/dev/ttyACM0"   # Modem/Arduino

# Windows (si supporté)
port-name = "COM1"
port-name = "COM3"
```

### baud-rate

**Type :** Integer  
**Valeurs courantes :** 9600, 19200, 38400, 57600, 115200  
**Défaut :** `9600`  
**Description :** Vitesse de transmission

```ini
[rs232]
baud-rate = 9600      # Standard pour anciens équipements
baud-rate = 19200     # Vitesse intermédiaire
baud-rate = 38400     # Vitesse élevée
baud-rate = 115200    # Très haute vitesse
```

### bits

**Type :** Integer  
**Valeurs :** 7, 8  
**Défaut :** `8`  
**Description :** Nombre de bits de données

```ini
[rs232]
bits = 7    # 7 bits (rare)
bits = 8    # 8 bits (standard)
```

### parity

**Type :** String  
**Valeurs :** `"NONE"`, `"EVEN"`, `"ODD"`  
**Défaut :** `"NONE"`  
**Description :** Contrôle de parité

```ini
[rs232]
parity = "NONE"    # Pas de parité (standard)
parity = "EVEN"    # Parité paire
parity = "ODD"     # Parité impaire
```

### stop-bits

**Type :** Integer  
**Valeurs :** 1, 2  
**Défaut :** `1`  
**Description :** Nombre de bits d'arrêt

```ini
[rs232]
stop-bits = 1    # 1 bit d'arrêt (standard)
stop-bits = 2    # 2 bits d'arrêt (rare)
```

### flow-control

**Type :** String  
**Valeurs :** `"NONE"`, `"HARDWARE"`, `"SOFTWARE"`  
**Défaut :** `"NONE"`  
**Description :** Contrôle de flux

```ini
[rs232]
flow-control = "NONE"        # Pas de contrôle (standard)
flow-control = "HARDWARE"    # RTS/CTS
flow-control = "SOFTWARE"    # XON/XOFF
```

## Section [hl7]

Configuration du protocole HL7.

### version

**Type :** String  
**Valeurs :** `"2.4"`, `"2.5"`, `"2.6"`  
**Défaut :** `"2.6"`  
**Description :** Version du standard HL7

```ini
[hl7]
version = "2.4"    # Version ancienne
version = "2.5"    # Version intermédiaire
version = "2.6"    # Version récente (recommandée)
```

### sending-application

**Type :** String  
**Défaut :** `"LAB"`  
**Description :** Nom de l'application émettrice

```ini
[hl7]
sending-application = "F200"        # Nom de l'automate
sending-application = "HEMATO"      # Type d'analyse
sending-application = "LAB_MAIN"    # Laboratoire principal
```

### sending-facility

**Type :** String  
**Défaut :** `"SYNLAB"`  
**Description :** Nom de l'établissement émetteur

```ini
[hl7]
sending-facility = "SYNLAB"           # Nom standard
sending-facility = "SYNLAB_PARIS"     # Site spécifique
sending-facility = "HOSPITAL_MAIN"    # Hôpital
```

## Section [poct]

Configuration du protocole POCT.

### device-id

**Type :** String  
**Défaut :** `"POCT001"`  
**Description :** Identifiant unique de l'appareil

```ini
[poct]
device-id = "GLUCO_001"      # Glucomètre
device-id = "CARDIO_002"     # Appareil cardiaque
device-id = "POCT_ROOM_A"    # Par localisation
```

## Section [storing]

Configuration du stockage des données.

### output-directory

**Type :** String  
**Défaut :** `"./data"`  
**Description :** Répertoire de stockage des messages

```ini
[storing]
output-directory = "./data"                    # Relatif
output-directory = "/var/data/synlab"          # Absolu
output-directory = "/fast/storage/synlab"      # Stockage rapide
```

### create-subdirs

**Type :** Boolean  
**Défaut :** `true`  
**Description :** Créer des sous-répertoires par date

```ini
[storing]
create-subdirs = true     # Structure : data/2023/12/01/
create-subdirs = false    # Tous les fichiers dans output-directory
```

## Exemples de configurations complètes

### Configuration minimale

```ini
[global]
log-level = "INFO"

[tcp]
bind-port = 1234

[hl7]
version = "2.6"

[storing]
output-directory = "./data"
```

### Configuration de production

```ini
[global]
log-level = "WARN"
daemonize = true
log-file = "/var/log/synlab/prod.log"

[tcp]
bind-ip = "0.0.0.0"
bind-port = 1234
recv-timeout = 300

[hl7]
version = "2.6"
sending-application = "PROD_F200"
sending-facility = "SYNLAB_MAIN"

[storing]
output-directory = "/var/data/synlab/prod"
create-subdirs = true
```

### Configuration de test

```ini
[global]
log-level = "DEBUG"
daemonize = false
log-file = "./test.log"
stop-after = 50

[tcp]
bind-ip = "127.0.0.1"
bind-port = 9999

[hl7]
version = "2.6"
sending-application = "TEST"
sending-facility = "DEV"

[storing]
output-directory = "./test_data"
create-subdirs = false
```

### Configuration RS232

```ini
[global]
log-level = "INFO"
log-file = "./rs232.log"

[rs232]
port-name = "/dev/ttyUSB0"
baud-rate = 19200
bits = 8
parity = "NONE"
stop-bits = 1
flow-control = "NONE"

[poct]
device-id = "SERIAL_001"

[storing]
output-directory = "./serial_data"
create-subdirs = true
```

## Validation de configuration

### Syntaxe

- Sections entre crochets : `[section]`
- Paramètres : `nom = valeur`
- Chaînes entre guillemets : `"valeur"`
- Nombres sans guillemets : `123`
- Booléens : `true` ou `false`

### Vérification

```bash
# Test de la configuration
./bin/driver -c ma_config.config --validate-config

# Ou démarrage en mode debug
./bin/driver -c ma_config.config
```

### Erreurs courantes

```ini
# ❌ INCORRECT
[global]
log-level = DEBUG        # Manque les guillemets
bind-port = "1234"       # Nombre entre guillemets
daemonize = yes          # Utiliser true/false

# ✅ CORRECT
[global]
log-level = "DEBUG"
bind-port = 1234
daemonize = true
```

## Prochaines étapes

- [**Exemples**](examples.md) - Configurations réelles
- [**Dépannage**](troubleshooting.md) - Résolution de problèmes
- [**FAQ**](faq.md) - Questions fréquentes
