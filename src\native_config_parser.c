/**
 * @file native_config_parser.c
 * @brief Implementation of native configuration file parser.
 */

#include "native_config_parser.h"
#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <errno.h>
#include <strings.h>  // For bzero on Linux

#define INITIAL_CAPACITY 8

/**
 * @brief Optimized memory allocation for Linux RedHat
 */
static inline void *optimized_malloc(size_t size) {
    // Align memory to cache line boundaries for better performance on Linux
    void *ptr = aligned_alloc(64, (size + 63) & ~63);
    if (__builtin_expect(!ptr, 0)) {
        log_error("Failed to allocate aligned memory of size %zu", size);
        return malloc(size); // Fallback to regular malloc
    }
    return ptr;
}

/**
 * @brief Trim whitespace from both ends of a string (optimized for Linux)
 */
static char *trim_whitespace(char *str) {
    char *end;

    // Trim leading space - optimized with likely branch prediction
    while (__builtin_expect(isspace((unsigned char)*str), 0)) str++;

    if (__builtin_expect(*str == 0, 0)) return str; // All spaces?

    // Trim trailing space
    end = str + strlen(str) - 1;
    while (end > str && isspace((unsigned char)*end)) end--;

    // Write new null terminator
    end[1] = '\0';

    return str;
}

/**
 * @brief Remove quotes from a string value
 */
static void remove_quotes(char *str) {
    size_t len = strlen(str);
    if (len >= 2 && str[0] == '"' && str[len-1] == '"') {
        memmove(str, str + 1, len - 2);
        str[len - 2] = '\0';
    }
}

/**
 * @brief Find or create a section in the parser
 */
static ConfigSection *find_or_create_section(ConfigParser *parser, const char *section_name) {
    // First, try to find existing section
    for (size_t i = 0; i < parser->section_count; i++) {
        if (strcmp(parser->sections[i].name, section_name) == 0) {
            return &parser->sections[i];
        }
    }
    
    // Section not found, create new one
    if (parser->section_count >= parser->section_capacity) {
        size_t new_capacity = parser->section_capacity * 2;
        ConfigSection *new_sections = realloc(parser->sections, 
                                             new_capacity * sizeof(ConfigSection));
        if (!new_sections) {
            log_error("Failed to allocate memory for sections");
            return NULL;
        }
        parser->sections = new_sections;
        parser->section_capacity = new_capacity;
    }
    
    ConfigSection *section = &parser->sections[parser->section_count];
    strncpy(section->name, section_name, MAX_CONFIG_SECTION_LENGTH - 1);
    section->name[MAX_CONFIG_SECTION_LENGTH - 1] = '\0';
    section->values = malloc(INITIAL_CAPACITY * sizeof(ConfigValue));
    if (!section->values) {
        log_error("Failed to allocate memory for section values");
        return NULL;
    }
    section->value_count = 0;
    section->value_capacity = INITIAL_CAPACITY;
    parser->section_count++;
    
    return section;
}

/**
 * @brief Add a value to a section
 */
static int add_value_to_section(ConfigSection *section, const char *key, 
                               const char *value_str) {
    if (section->value_count >= section->value_capacity) {
        size_t new_capacity = section->value_capacity * 2;
        ConfigValue *new_values = realloc(section->values, 
                                         new_capacity * sizeof(ConfigValue));
        if (!new_values) {
            log_error("Failed to allocate memory for values");
            return 1;
        }
        section->values = new_values;
        section->value_capacity = new_capacity;
    }
    
    ConfigValue *config_value = &section->values[section->value_count];
    strncpy(config_value->key, key, MAX_CONFIG_KEY_LENGTH - 1);
    config_value->key[MAX_CONFIG_KEY_LENGTH - 1] = '\0';
    
    // Determine value type and parse accordingly
    char value_copy[MAX_CONFIG_VALUE_LENGTH];
    strncpy(value_copy, value_str, MAX_CONFIG_VALUE_LENGTH - 1);
    value_copy[MAX_CONFIG_VALUE_LENGTH - 1] = '\0';
    
    // Check if it's a quoted string
    if (value_copy[0] == '"') {
        config_value->type = CONFIG_TYPE_STRING;
        remove_quotes(value_copy);
        strncpy(config_value->value.string_value, value_copy, MAX_CONFIG_VALUE_LENGTH - 1);
        config_value->value.string_value[MAX_CONFIG_VALUE_LENGTH - 1] = '\0';
    }
    // Check if it's a boolean
    else if (strcmp(value_copy, "true") == 0 || strcmp(value_copy, "false") == 0) {
        config_value->type = CONFIG_TYPE_BOOL;
        config_value->value.bool_value = (strcmp(value_copy, "true") == 0);
    }
    // Otherwise, try to parse as integer
    else {
        char *endptr;
        long int_val = strtol(value_copy, &endptr, 10);
        if (*endptr == '\0') {
            config_value->type = CONFIG_TYPE_INT;
            config_value->value.int_value = int_val;
        } else {
            // If not a valid integer, treat as unquoted string
            config_value->type = CONFIG_TYPE_STRING;
            strncpy(config_value->value.string_value, value_copy, MAX_CONFIG_VALUE_LENGTH - 1);
            config_value->value.string_value[MAX_CONFIG_VALUE_LENGTH - 1] = '\0';
        }
    }
    
    section->value_count++;
    return 0;
}

int config_parser_init(ConfigParser *parser) {
    if (__builtin_expect(!parser, 0)) {
        log_error("Parser pointer is NULL");
        return 1;
    }

    // Use optimized allocation for better performance on Linux RedHat
    parser->sections = optimized_malloc(INITIAL_CAPACITY * sizeof(ConfigSection));
    if (__builtin_expect(!parser->sections, 0)) {
        log_error("Failed to allocate memory for parser sections");
        return 1;
    }

    parser->section_count = 0;
    parser->section_capacity = INITIAL_CAPACITY;

    return 0;
}

int config_parser_parse_file(ConfigParser *parser, const char *filename) {
    if (__builtin_expect(!parser || !filename, 0)) {
        log_error("Invalid parameters for config_parser_parse_file");
        return 1;
    }

    FILE *file = fopen(filename, "r");
    if (__builtin_expect(!file, 0)) {
        log_error("Failed to open configuration file %s: %s", filename, strerror(errno));
        return 1;
    }

    // Use larger buffer for better I/O performance on Linux
    char line[MAX_CONFIG_LINE_LENGTH];
    int line_number = 0;
    ConfigSection *current_section = NULL;

    // Set larger buffer for file I/O on Linux
    if (setvbuf(file, NULL, _IOFBF, 8192) != 0) {
        log_warn("Failed to set file buffer size, continuing with default");
    }

    while (fgets(line, sizeof(line), file)) {
        line_number++;

        // Remove newline character
        char *newline = strchr(line, '\n');
        if (newline) *newline = '\0';

        // Trim whitespace
        char *trimmed_line = trim_whitespace(line);

        // Skip empty lines and comments
        if (*trimmed_line == '\0' || *trimmed_line == '#') {
            continue;
        }

        // Check for section start
        char *brace_pos = strchr(trimmed_line, '{');
        if (brace_pos) {
            *brace_pos = '\0';
            char *section_name = trim_whitespace(trimmed_line);
            current_section = find_or_create_section(parser, section_name);
            if (!current_section) {
                log_error("Failed to create section '%s' at line %d", section_name, line_number);
                fclose(file);
                return 1;
            }
            continue;
        }

        // Check for section end
        if (strchr(trimmed_line, '}')) {
            current_section = NULL;
            continue;
        }

        // Parse key-value pair
        if (current_section) {
            char *equals_pos = strchr(trimmed_line, '=');
            if (equals_pos) {
                *equals_pos = '\0';
                char *key = trim_whitespace(trimmed_line);
                char *value = trim_whitespace(equals_pos + 1);

                if (add_value_to_section(current_section, key, value) != 0) {
                    log_error("Failed to add value '%s = %s' at line %d", key, value, line_number);
                    fclose(file);
                    return 1;
                }
            } else {
                log_error("Invalid syntax at line %d: %s", line_number, trimmed_line);
                fclose(file);
                return 1;
            }
        } else {
            log_error("Key-value pair outside of section at line %d: %s", line_number, trimmed_line);
            fclose(file);
            return 1;
        }
    }

    fclose(file);
    return 0;
}

/**
 * @brief Find a section by name (optimized for Linux with branch prediction)
 */
static const ConfigSection *find_section(const ConfigParser *parser, const char *section_name) {
    // Use likely/unlikely hints for better branch prediction on Linux
    for (size_t i = 0; i < parser->section_count; i++) {
        if (__builtin_expect(strcmp(parser->sections[i].name, section_name) == 0, 0)) {
            return &parser->sections[i];
        }
    }
    return NULL;
}

/**
 * @brief Find a value in a section by key
 */
static const ConfigValue *find_value(const ConfigSection *section, const char *key) {
    for (size_t i = 0; i < section->value_count; i++) {
        if (strcmp(section->values[i].key, key) == 0) {
            return &section->values[i];
        }
    }
    return NULL;
}

const char *config_parser_get_string(const ConfigParser *parser,
                                     const char *section_name,
                                     const char *key,
                                     const char *default_value) {
    if (!parser || !section_name || !key) {
        return default_value;
    }

    const ConfigSection *section = find_section(parser, section_name);
    if (!section) {
        return default_value;
    }

    const ConfigValue *value = find_value(section, key);
    if (!value || value->type != CONFIG_TYPE_STRING) {
        return default_value;
    }

    return value->value.string_value;
}

long config_parser_get_int(const ConfigParser *parser,
                          const char *section_name,
                          const char *key,
                          long default_value) {
    if (!parser || !section_name || !key) {
        return default_value;
    }

    const ConfigSection *section = find_section(parser, section_name);
    if (!section) {
        return default_value;
    }

    const ConfigValue *value = find_value(section, key);
    if (!value || value->type != CONFIG_TYPE_INT) {
        return default_value;
    }

    return value->value.int_value;
}

bool config_parser_get_bool(const ConfigParser *parser,
                           const char *section_name,
                           const char *key,
                           bool default_value) {
    if (!parser || !section_name || !key) {
        return default_value;
    }

    const ConfigSection *section = find_section(parser, section_name);
    if (!section) {
        return default_value;
    }

    const ConfigValue *value = find_value(section, key);
    if (!value || value->type != CONFIG_TYPE_BOOL) {
        return default_value;
    }

    return value->value.bool_value;
}

bool config_parser_has_key(const ConfigParser *parser,
                          const char *section_name,
                          const char *key) {
    if (!parser || !section_name || !key) {
        return false;
    }

    const ConfigSection *section = find_section(parser, section_name);
    if (!section) {
        return false;
    }

    const ConfigValue *value = find_value(section, key);
    return (value != NULL);
}

void config_parser_cleanup(ConfigParser *parser) {
    if (__builtin_expect(!parser, 0)) {
        return;
    }

    for (size_t i = 0; i < parser->section_count; i++) {
        if (parser->sections[i].values) {
            free(parser->sections[i].values);
        }
    }

    if (parser->sections) {
        free(parser->sections);
    }

    // Clear the structure efficiently on Linux
    bzero(parser, sizeof(ConfigParser));
}
