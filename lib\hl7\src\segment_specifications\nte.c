#include "sac.h"

#if HL7_VERSION == 240 || HL7_VERSION == 250

// clang-format off
const FieldInSegmentSpec NTE_Spec[4] = {
    {"set id / nte", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"source of comment", FieldTypeID, 8, OPTIONAL, NOT_REPEATABLE, TableType0105},
    {"comment", FieldTypeFT, 65536, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"comment type", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0364},
};
// clang-format on

#elif HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec NTE_Spec[8] = {
    {"set id / nte", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"source of comment", FieldTypeID, 8, O<PERSON><PERSON>ON<PERSON>, NOT_REPEATABLE, TableType0105},
    {"comment", FieldTypeFT, 65536, OP<PERSON><PERSON><PERSON>, REPEA<PERSON><PERSON>E_INFINITE, TABLE_NONE},
    {"comment type", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0364},
    {"entered by", FieldTypeXCN, 3220, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"entered date/time", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"effective start date", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"expiration date", FieldTypeDTM, 24, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#endif