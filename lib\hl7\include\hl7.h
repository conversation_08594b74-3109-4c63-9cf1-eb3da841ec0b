/**
 * @file hl7.h
 * @brief HL7 message parsing library.
 *
 * All specifications were extracted from
 * https://hl7-definition.caristix.com/v2/.
 */

#pragma once

#include "common.h"
#include "message.h"
#include "specification.h"
#include "validation.h"

#include "ce.h"
#include "cne.h"
#include "cq.h"
#include "cwe.h"
#include "cx.h"
#include "dtm.h"
#include "ei.h"
#include "hd.h"
#include "id.h"
#include "is.h"
#include "log.h"
#include "msg.h"
#include "nm.h"
#include "pt.h"
#include "si.h"
#include "st.h"
#include "ts.h"
#include "varies.h"
#include "vid.h"
#include "xcn.h"
#include "xpn.h"

#include "msa.h"
#include "msh.h"
#include "obr.h"
#include "obx.h"
#include "pid.h"

#include "table_0003.h"
#include "table_0008.h"
#include "table_0065.h"
#include "table_0076.h"
#include "table_0085.h"
#include "table_0102.h"
#include "table_0103.h"
#include "table_0104.h"
#include "table_0125.h"
#include "table_0155.h"
#include "table_0163.h"
#include "table_0200.h"
#include "table_0211.h"
#include "table_0301.h"
#include "table_0354.h"
#include "table_0357.h"
#include "table_0360.h"
#include "table_0396.h"
#include "table_0725.h"

// TODO: instructions on how to add HL7 specifications

/**
 * @brief Generate a random control ID for a hl7 message.
 *
 * @return constant string representing a control ID
 */
const char *hl7_generate_control_id();

/**
 * @brief Generates a string representation of the current time
 *
 * The format is "YYYYMMDDHHMMSS-ZZZZ"
 *
 * @return a string representation of the current time
 */
const char *hl7_generate_date_time();

// TODO: tables enums with str values for safer message creation