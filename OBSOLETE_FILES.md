# Fichiers Obsolètes Suite à la Refonte de Configuration

## Vue d'ensemble

Suite à la refonte vers un système de configuration unifié, plusieurs fichiers de parsing de configuration individuels sont devenus **obsolètes** et peuvent être supprimés du projet.

## ⚠️ Fichiers à Supprimer

### 1. Parsers de Configuration Globale
- `src/global_config_file.c` - Parser pour global.config
- `include/global_config_file.h` - Header du parser global

### 2. Parsers de Configuration Transport

#### TCP
- `src/transport_layer/tcp/tcp_config_file.c` - Parser pour tcp.config
- `include/transport_layer/tcp/tcp_config_file.h` - Header du parser TCP

#### RS232
- `src/transport_layer/rs232/rs232_config_file.c` - Parser pour rs232.config
- `include/transport_layer/rs232/rs232_config_file.h` - Header du parser RS232

### 3. Parsers de Configuration Application

#### HL7
- `src/application_layer/hl7/hl7_config_file.c` - Parser pour hl7.config
- `include/application_layer/hl7/hl7_config_file.h` - Header du parser HL7

#### POCT
- `src/application_layer/poct/poct_config_file.c` - Parser pour poct.config
- `include/application_layer/poct/poct_config_file.h` - Header du parser POCT

### 4. Parsers de Configuration Stockage

#### File Storing
- `src/storing_layer/file_storing/file_storing_config_file.c` - Parser pour storing.config
- `include/storing_layer/file_storing/file_storing_config_file.h` - Header du parser storing

## ✅ Fichiers à Conserver

Ces fichiers restent nécessaires car ils définissent les structures de données et les fonctions utilitaires :

### Structures de Configuration
- `src/global_config.c` + `include/global_config.h` - Structure GlobalConfiguration
- `src/transport_layer/tcp/tcp_config.c` + `include/transport_layer/tcp/tcp_config.h`
- `src/transport_layer/rs232/rs232_config.c` + `include/transport_layer/rs232/rs232_config.h`
- `src/application_layer/hl7/hl7_config.c` + `include/application_layer/hl7/hl7_config.h`
- `src/application_layer/poct/poct_config.c` + `include/application_layer/poct/poct_config.h`
- `src/storing_layer/file_storing/file_storing_config.c` + `include/storing_layer/file_storing/file_storing_config.h`

### Nouveaux Fichiers (Remplacements)
- `src/unified_config_file.c` + `include/unified_config_file.h` - Parser unifié

## 📋 Script de Nettoyage

Voici un script PowerShell pour supprimer automatiquement les fichiers obsolètes :

```powershell
# Fichiers obsolètes à supprimer
$obsoleteFiles = @(
    "src/global_config_file.c",
    "include/global_config_file.h",
    "src/transport_layer/tcp/tcp_config_file.c",
    "include/transport_layer/tcp/tcp_config_file.h",
    "src/transport_layer/rs232/rs232_config_file.c",
    "include/transport_layer/rs232/rs232_config_file.h",
    "src/application_layer/hl7/hl7_config_file.c",
    "include/application_layer/hl7/hl7_config_file.h",
    "src/application_layer/poct/poct_config_file.c",
    "include/application_layer/poct/poct_config_file.h",
    "src/storing_layer/file_storing/file_storing_config_file.c",
    "include/storing_layer/file_storing/file_storing_config_file.h"
)

Write-Host "Suppression des fichiers obsolètes..." -ForegroundColor Yellow

foreach ($file in $obsoleteFiles) {
    if (Test-Path $file) {
        Remove-Item $file -Force
        Write-Host "✓ Supprimé: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠ Déjà absent: $file" -ForegroundColor Gray
    }
}

Write-Host "Nettoyage terminé!" -ForegroundColor Green
```

## 🔧 Modifications Nécessaires dans le Build System

Après suppression des fichiers obsolètes, vous devrez peut-être mettre à jour :

### CMakeLists.txt
Supprimer les références aux anciens fichiers de parsing dans les listes de sources.

### Dépendances
Vérifier qu'aucun autre fichier n'inclut les headers obsolètes.

## ⚡ Avantages du Nettoyage

1. **Réduction de la complexité** : Moins de fichiers à maintenir
2. **Éviter la confusion** : Plus de risque d'utiliser les anciens parsers
3. **Taille du projet** : Réduction de ~12 fichiers source
4. **Maintenance** : Un seul point de parsing à maintenir

## 🚨 Précautions

Avant de supprimer les fichiers :

1. **Sauvegardez** votre projet (commit Git recommandé)
2. **Testez** que la compilation fonctionne avec le nouveau système
3. **Vérifiez** qu'aucun autre code ne référence ces fichiers
4. **Documentez** la suppression pour l'équipe

## 📊 Résumé

- **Fichiers à supprimer** : 12 fichiers (6 .c + 6 .h)
- **Fichiers remplacés par** : 2 fichiers (unified_config_file.c + .h)
- **Réduction nette** : -10 fichiers
- **Fonctionnalité** : Identique, mais centralisée

Cette suppression complète la refonte vers le système de configuration unifié et simplifie grandement la maintenance du projet.
