#pragma once

#include <stddef.h>

#define CONFIG_RESPONSE_TIMEOUT "response-timeout-ms"

typedef struct {
  size_t response_timeout_ms; // time before sending response, in milliseconds
} ApplicationConfiguration;

void application_configuration_init(ApplicationConfiguration *config);
void application_configuration_destruct(ApplicationConfiguration *config);
char *
application_configuration_to_string(const ApplicationConfiguration *config);
