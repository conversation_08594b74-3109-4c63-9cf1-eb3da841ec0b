# Codes d'erreur

Cette page liste les codes d'erreur et messages que peuvent générer les drivers d'automates Synlab.

## Codes de retour du programme

| Code | Signification | Description |
|------|---------------|-------------|
| 0 | SUCCESS | Exécution normale |
| 1 | CONFIG_ERROR | Erreur de configuration |
| 2 | NETWORK_ERROR | Erreur réseau/communication |
| 3 | FILE_ERROR | Erreur de fichier/stockage |
| 4 | PERMISSION_ERROR | Erreur de permissions |
| 5 | RESOURCE_ERROR | Ressource indisponible |

## Messages d'erreur de configuration

### CONFIG_001 - Fichier de configuration introuvable

```
ERROR: Configuration file not found: /path/to/config.conf
```

**Cause :** Le fichier spécifié avec `-c` n'existe pas.

**Solution :**
```bash
# Vérifier le chemin
ls -la /path/to/config.conf

# Utiliser un chemin absolu
./driver -c /full/path/to/config.conf
```

### CONFIG_002 - Erreur de syntaxe

```
ERROR: Configuration syntax error at line 15: Invalid parameter 'bind-port'
```

**Cause :** Paramètre mal écrit ou valeur incorrecte.

**Solution :**
```ini
# ❌ Incorrect
bind-port = "1234"    # Nombre entre guillemets

# ✅ Correct  
bind-port = 1234
```

### CONFIG_003 - Section manquante

```
ERROR: Required section [tcp] not found in configuration
```

**Cause :** Section obligatoire absente du fichier.

**Solution :** Ajouter la section manquante :
```ini
[tcp]
bind-ip = "127.0.0.1"
bind-port = 1234
```

### CONFIG_004 - Paramètre obligatoire manquant

```
ERROR: Required parameter 'bind-port' missing in section [tcp]
```

**Solution :** Ajouter le paramètre manquant.

### CONFIG_005 - Valeur invalide

```
ERROR: Invalid value 'INVALID' for parameter 'log-level'. Expected: DEBUG, INFO, WARN, ERROR
```

**Solution :** Utiliser une valeur valide :
```ini
[global]
log-level = "INFO"    # Valeur correcte
```

## Messages d'erreur réseau

### NET_001 - Port déjà utilisé

```
ERROR: Cannot bind to port 1234: Address already in use
```

**Diagnostic :**
```bash
sudo netstat -tlnp | grep 1234
```

**Solutions :**
- Changer le port dans la configuration
- Arrêter l'autre processus
- Attendre la libération du port

### NET_002 - Adresse IP invalide

```
ERROR: Cannot bind to IP address '192.168.1.999': Invalid address
```

**Solution :** Corriger l'adresse IP :
```ini
[tcp]
bind-ip = "*************"    # IP valide
```

### NET_003 - Interface réseau indisponible

```
ERROR: Cannot bind to IP address '*************': Cannot assign requested address
```

**Cause :** L'interface réseau n'existe pas ou n'est pas configurée.

**Diagnostic :**
```bash
ip addr show
ifconfig
```

### NET_004 - Timeout de connexion

```
WARN: Connection timeout after 120 seconds
```

**Solutions :**
```ini
[tcp]
recv-timeout = 300    # Augmenter le timeout
```

### NET_005 - Connexion fermée brutalement

```
ERROR: Connection reset by peer
```

**Causes possibles :**
- L'automate a redémarré
- Problème réseau
- Firewall qui bloque

## Messages d'erreur série (RS232)

### SERIAL_001 - Port série introuvable

```
ERROR: Serial port '/dev/ttyUSB0' not found
```

**Diagnostic :**
```bash
ls -la /dev/ttyUSB* /dev/ttyS*
lsusb | grep -i serial
dmesg | grep tty
```

### SERIAL_002 - Permission refusée

```
ERROR: Cannot open serial port '/dev/ttyUSB0': Permission denied
```

**Solution :**
```bash
sudo usermod -a -G dialout $USER
newgrp dialout
```

### SERIAL_003 - Port série occupé

```
ERROR: Serial port '/dev/ttyUSB0' is already in use
```

**Diagnostic :**
```bash
sudo lsof /dev/ttyUSB0
ps aux | grep tty
```

### SERIAL_004 - Configuration série invalide

```
ERROR: Invalid baud rate '9601'. Supported rates: 9600, 19200, 38400, 57600, 115200
```

**Solution :** Utiliser une vitesse supportée :
```ini
[rs232]
baud-rate = 9600    # Vitesse standard
```

## Messages d'erreur de protocole

### HL7_001 - Message HL7 malformé

```
ERROR: Invalid HL7 message: Missing MSH segment
```

**Cause :** Le message reçu n'a pas la structure HL7 correcte.

**Diagnostic :** Examiner le message brut :
```bash
hexdump -C message_brut.txt
cat -A message_brut.txt
```

### HL7_002 - Version HL7 non supportée

```
ERROR: Unsupported HL7 version '2.3' in MSH segment
```

**Solution :** Configurer la bonne version :
```ini
[hl7]
version = "2.6"    # Version supportée
```

### HL7_003 - Segment obligatoire manquant

```
WARN: Required segment 'PID' missing in HL7 message
```

**Cause :** Message HL7 incomplet.

### POCT_001 - Format POCT invalide

```
ERROR: Invalid POCT message format
```

**Cause :** Le message ne respecte pas le format POCT attendu.

## Messages d'erreur de stockage

### STORAGE_001 - Répertoire de stockage inaccessible

```
ERROR: Cannot access output directory '/var/data/synlab': No such file or directory
```

**Solution :**
```bash
sudo mkdir -p /var/data/synlab
sudo chown $USER:$USER /var/data/synlab
```

### STORAGE_002 - Permissions d'écriture insuffisantes

```
ERROR: Cannot create file '/var/data/synlab/message.hl7': Permission denied
```

**Solution :**
```bash
sudo chown -R $USER:$USER /var/data/synlab
sudo chmod -R 755 /var/data/synlab
```

### STORAGE_003 - Espace disque insuffisant

```
ERROR: Cannot write file '/var/data/synlab/message.hl7': No space left on device
```

**Diagnostic :**
```bash
df -h /var/data/synlab
```

**Solutions :**
- Nettoyer les anciens fichiers
- Augmenter l'espace disque
- Changer le répertoire de stockage

### STORAGE_004 - Nom de fichier invalide

```
ERROR: Cannot create file with invalid characters in name
```

**Cause :** Caractères spéciaux dans le nom de fichier généré.

## Messages d'erreur système

### SYS_001 - Mémoire insuffisante

```
ERROR: Cannot allocate memory: Out of memory
```

**Diagnostic :**
```bash
free -h
ps aux --sort=-%mem | head
```

### SYS_002 - Trop de fichiers ouverts

```
ERROR: Cannot open file: Too many open files
```

**Solution :**
```bash
# Augmenter la limite
ulimit -n 4096

# Ou dans /etc/security/limits.conf
synlab soft nofile 4096
synlab hard nofile 8192
```

### SYS_003 - Signal reçu

```
INFO: Received SIGTERM, shutting down gracefully
INFO: Received SIGINT, shutting down gracefully
```

**Cause :** Arrêt normal du programme (Ctrl+C ou kill).

## Messages d'avertissement

### WARN_001 - Configuration par défaut utilisée

```
WARN: Parameter 'recv-timeout' not specified, using default value 120
```

**Action :** Spécifier explicitement la valeur si nécessaire.

### WARN_002 - Message dupliqué

```
WARN: Duplicate message ID 'MSG_123456' received
```

**Cause :** L'automate a renvoyé le même message.

### WARN_003 - Connexion lente

```
WARN: Slow connection detected: 30 seconds to receive message
```

**Causes possibles :**
- Réseau lent
- Automate surchargé
- Gros volume de données

## Messages d'information

### INFO_001 - Démarrage

```
INFO: Driver started successfully, listening on *************:1234
INFO: Using configuration file: /etc/synlab/driver.config
INFO: Log level set to INFO
```

### INFO_002 - Connexion

```
INFO: New connection from ************:45678
INFO: Connection established with client
```

### INFO_003 - Message traité

```
INFO: Message processed successfully: ORU^R01 from F200
INFO: Data stored in: /var/data/synlab/2023/12/01/message_001.hl7
```

### INFO_004 - Arrêt

```
INFO: Shutdown signal received, stopping gracefully
INFO: All connections closed
INFO: Driver stopped
```

## Diagnostic des erreurs

### Script de diagnostic

```bash
#!/bin/bash
# error_diagnosis.sh

LOG_FILE="/var/log/synlab/driver.log"

echo "=== Analyse des erreurs ==="

# Compter les erreurs par type
echo "Erreurs de configuration:"
grep "CONFIG_" $LOG_FILE | wc -l

echo "Erreurs réseau:"
grep "NET_\|NETWORK" $LOG_FILE | wc -l

echo "Erreurs série:"
grep "SERIAL_" $LOG_FILE | wc -l

echo "Erreurs de stockage:"
grep "STORAGE_" $LOG_FILE | wc -l

echo "=== Dernières erreurs ==="
grep -i error $LOG_FILE | tail -5
```

### Niveaux de gravité

| Niveau | Description | Action |
|--------|-------------|--------|
| **ERROR** | Erreur critique | Intervention immédiate |
| **WARN** | Avertissement | Surveillance |
| **INFO** | Information | Aucune action |
| **DEBUG** | Debug | Développement uniquement |

## Prochaines étapes

- [**Dépannage**](troubleshooting.md) - Solutions détaillées
- [**FAQ**](faq.md) - Questions fréquentes
- [**Configuration**](config-reference.md) - Référence complète
