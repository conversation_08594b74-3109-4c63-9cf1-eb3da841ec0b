/**
 * @file tcp_server.h
 * @brief Simple TCP server.
 */

#pragma once

#include <netinet/in.h>
#include <netinet/ip.h>
#include <stdbool.h>
#include <stddef.h>
#include <sys/socket.h>
#include <sys/types.h>

struct sockaddr_in;

/**
 * @brief Definition of a TCP server.
 */
typedef struct StructTcpServer {
  /**
   * @brief IP address of the interface through which the server will listen for
   * incoming connection requests.
   *
   * @note If this value is set to NULL, then the server will listen for
   * incoming connection requests through all available interfaces.
   */
  char *ip;
  /**
   * @brief The port number that will be bound to the server network address.
   */
  unsigned short port;
  /**
   * @brief Flag that tells whether the option SO_REUSEADDR must be set on the
   * server socket or not.
   *
   * @note The defaut value the "TRUE" (1).
   */
  int reuse_address;
  /**
   * @brief Flag that tells whether the option SO_REUSEPORT must be set on the
   * server socket or not.
   *
   * @note Defaults to "TRUE" (1).
   */
  int reuse_port;
  /**
   * @brief The descriptor of the socket used by the server to accept incoming
   * connection requests.
   */
  int socket;
  /**
   * @brief maximum length to which the queue of pending connections may grow
   *
   * @note The default value for this parameter is 1.
   */
  int max_connexions;
  /**
   * @brief Maximum number of seconds to wait for a message from the client.
   * If you don't want to configure a read timeout, just set this parameter's
   * value to 0.
   *
   * @note The default value for this parameter is 0 (no timeout).
   */
  time_t read_timeout;

} TcpServer;

/**
 * @brief Create a new TCP server.
 *
 * @param ip [optional] Address of a zero-terminated string of characters that
 * represents the IP address associated with the interface used by the server
 * for listening to incoming connection requests. If the value of this parameter
 * is NULL, then the server will listen to incoming connection requests on all
 * available interfaces.
 * @param port Port number used to construct the network address of the server.
 *
 * @return
 * - Success : The address of a new TCP server.
 *  - Error : NULL Please note that the only error that can occur is that the
 * process runs out of memory.
 */
TcpServer *tcp_server_create(const char *ip, const unsigned short port);

/**
 * @brief Initialize the TCP server: create the socket used to listen to
 * incoming connection requests.
 *
 * @note A call to this function only creates a socket.
 *
 * @param tcp_server Address of the TCP server object.
 * @return
 * - Success : 0
 * - Error : 1
 */
int tcp_server_init(TcpServer *tcp_server);

void tcp_server_set_reuse_address(TcpServer *tcp_server, int reuse_address);
void tcp_server_set_reuse_port(TcpServer *tcp_server, int reuse_port);
void tcp_server_set_read_timeout(TcpServer *tcp_server, time_t timeout);

/**
 * @brief Wait for a connection request and accepts it.
 *
 * This wait is blocking, but can be stopped with the stop flag.
 *
 * @param tcp_server
 * @param client `sockaddr_in` structure used to store data identifying the
 * address of the client whose connection has been accepted
 * @param namelen integer used to store the actual length of the
 * client's address. Please read the man page for the function `accept` in
 * order to get more information about this parameter.
 * @param stop stop flag pointer. When the flag is true, the function stops
 * listening for incoming requests.
 *
 * @return
 * - Success : A positive value that represents the descriptor of the socket
 * that handles the connection between the client and the server.
 * - Error : -1 In this case, the error is recorded.
 */
int tcp_server_listen_for_connexion_requests(TcpServer *tcp_server,
                                             struct sockaddr_in *client,
                                             int *namelen, const bool *stop);

/**
 * @brief Read data from a client.
 *
 * @note You should test the values referenced by the parameters
 * `connexion_closed` and `count`.
 *
 * @param client_socket The descriptor of the socket that identifies the
 * connection between the server and the client.
 * @param buffer_capacity Maximum number of bytes to read from the client.
 * @param connexion_closed Address of a flag that indicates whether the
 * connection with the client has been closed or not.
 *        - The value 0 means that the connection was closed (by the client).
 *        - The value 1 means that the connection was *NOT* closed (by the
 * client). And, thus, the connection is still open.
 * @param count Address of an integer used to store the actual number of bytes
 * read from the client. Please note that if the value referenced by this
 * parameter is 0, then it means that the connection to the client has been
 * closed.
 *
 * @return
 * - Success : The address of a dynamically allocated memory area that
 * contains the received bytes. Please note that the value of the last byte of
 * the memory area is always 0.
 * - Error : NULL
 */
unsigned char *tcp_server_read(int client_socket, size_t buffer_capacity,
                               int *connexion_closed, ssize_t *count);

/**
 * @brief Send bytes to the client.
 *
 * @param client_socket The descriptor of the socket that identifies the
 * connection to the client.
 * @param buffer Address of a memory area that contains the bytes to send.
 * @param length The number of bytes to send.
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int tcp_server_send(int client_socket, const unsigned char *buffer,
                    size_t length);

/**
 * @brief Closes the current connection.
 *
 * @param tcp_server Address of the tcp server.
 */
void tcp_server_terminate(TcpServer *tcp_server);

/**
 * @brief Frees a TcpServer, liberating allocated memory.
 *
 * Calls `tcp_server_terminate` beforehand, thus closing the connection before
 * freeing server.
 *
 * @param tcp_server Address of the tcp server.
 */
void tcp_server_destruct(TcpServer *tcp_server);