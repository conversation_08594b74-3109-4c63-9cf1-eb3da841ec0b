#pragma once

#include <stddef.h>
#include <time.h>

#define TIMESTAMP_BUFFER_CAPACITY 16

typedef enum { FT_TEXTUAL, FT_BINARY } FileType;

const char *file_type_get_extension(FileType ft);

typedef struct {
  const unsigned char *bytes;
  size_t length;
} BinaryFileData;

typedef struct {
  const char *message;
} TextualFileData;

typedef struct {
  FileType file_type;
  const void *file_data;
  const char *folder;
  const char *name;
} ApplicationToStoringData;