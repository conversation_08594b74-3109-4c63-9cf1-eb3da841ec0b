# Script de nettoyage des fichiers obsolètes
# Suite à la refonte vers la configuration unifiée

Write-Host "=== Nettoyage des Fichiers Obsolètes ===" -ForegroundColor Cyan
Write-Host "Suite à la refonte vers la configuration unifiée" -ForegroundColor Gray
Write-Host ""

# Liste des fichiers obsolètes à supprimer
$obsoleteFiles = @(
    "src/global_config_file.c",
    "include/global_config_file.h",
    "src/transport_layer/tcp/tcp_config_file.c",
    "include/transport_layer/tcp/tcp_config_file.h",
    "src/transport_layer/rs232/rs232_config_file.c",
    "include/transport_layer/rs232/rs232_config_file.h",
    "src/application_layer/hl7/hl7_config_file.c",
    "include/application_layer/hl7/hl7_config_file.h",
    "src/application_layer/poct/poct_config_file.c",
    "include/application_layer/poct/poct_config_file.h",
    "src/storing_layer/file_storing/file_storing_config_file.c",
    "include/storing_layer/file_storing/file_storing_config_file.h"
)

# Fonction pour demander confirmation
function Get-UserConfirmation {
    param([string]$Message)
    
    do {
        $response = Read-Host "$Message (o/n)"
        $response = $response.ToLower()
    } while ($response -ne "o" -and $response -ne "n" -and $response -ne "oui" -and $response -ne "non")
    
    return ($response -eq "o" -or $response -eq "oui")
}

# Vérifier que nous sommes dans le bon répertoire
if (-not (Test-Path "CMakeLists.txt")) {
    Write-Host "ERREUR: CMakeLists.txt non trouvé." -ForegroundColor Red
    Write-Host "Exécutez ce script depuis la racine du projet." -ForegroundColor Red
    exit 1
}

Write-Host "Fichiers qui seront supprimés :" -ForegroundColor Yellow
foreach ($file in $obsoleteFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file (existe)" -ForegroundColor Green
    } else {
        Write-Host "  - $file (déjà absent)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "ATTENTION: Cette opération est irréversible!" -ForegroundColor Red
Write-Host "Assurez-vous d'avoir sauvegardé votre projet (commit Git recommandé)." -ForegroundColor Yellow
Write-Host ""

# Demander confirmation
if (-not (Get-UserConfirmation "Voulez-vous continuer avec la suppression")) {
    Write-Host "Opération annulée." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "Suppression en cours..." -ForegroundColor Yellow

$suppressedCount = 0
$alreadyAbsentCount = 0

foreach ($file in $obsoleteFiles) {
    if (Test-Path $file) {
        try {
            Remove-Item $file -Force
            Write-Host "✓ Supprimé: $file" -ForegroundColor Green
            $suppressedCount++
        } catch {
            Write-Host "✗ Erreur lors de la suppression de: $file" -ForegroundColor Red
            Write-Host "  Erreur: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ Déjà absent: $file" -ForegroundColor Gray
        $alreadyAbsentCount++
    }
}

Write-Host ""
Write-Host "=== Résumé du Nettoyage ===" -ForegroundColor Cyan
Write-Host "Fichiers supprimés: $suppressedCount" -ForegroundColor Green
Write-Host "Fichiers déjà absents: $alreadyAbsentCount" -ForegroundColor Gray
Write-Host "Total traité: $($obsoleteFiles.Count)" -ForegroundColor White

if ($suppressedCount -gt 0) {
    Write-Host ""
    Write-Host "=== Prochaines Étapes Recommandées ===" -ForegroundColor Yellow
    Write-Host "1. Vérifiez que le projet compile toujours:"
    Write-Host "   mkdir build && cd build"
    Write-Host "   cmake .. -DTCP=ON -DHL7=ON -DFILE_STORING=ON"
    Write-Host "   make"
    Write-Host ""
    Write-Host "2. Testez avec la nouvelle configuration:"
    Write-Host "   ./f200 -c ../tests/sample_configs/unified.config"
    Write-Host ""
    Write-Host "3. Committez les changements:"
    Write-Host "   git add -A"
    Write-Host "   git commit -m 'Suppression des fichiers de parsing obsolètes'"
}

Write-Host ""
Write-Host "Nettoyage terminé!" -ForegroundColor Green
