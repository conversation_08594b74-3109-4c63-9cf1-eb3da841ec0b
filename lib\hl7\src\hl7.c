#include "hl7.h"

#include <stdlib.h>
#include <time.h>

#define DATE_LENGTH 30

#define TIME_INCLUDE_MS false
#define TIME_INCLUDE_TIMEZONE false

const char *hl7_generate_date_time() {
  static char timestamp[DATE_LENGTH];
  time_t t;
  struct tm *tmp;
  struct timespec ts;

  t = time(NULL);
  tmp = localtime(&t);
  //  nanoseconds precision
  clock_gettime(CLOCK_REALTIME, &ts);

  strftime(timestamp, DATE_LENGTH, "%Y%m%d%H%M%S", tmp); // Basic format

#if TIME_INCLUDE_MS
  // add fractional seconds (e.g., .SSS)
  snprintf(timestamp + strlen(timestamp), DATE_LENGTH - strlen(timestamp),
           ".%03ld", ts.tv_nsec / 1000000);
#endif

#if TIME_INCLUDE_TIMEZONE
  // add timezone offset
  strftime(timestamp + strlen(timestamp), DATE_LENGTH - strlen(timestamp), "%z",
           tmp);
#endif

  return timestamp;
}

#define CONTROL_ID_LENGTH (DATE_LENGTH + 10)

const char *hl7_generate_control_id() {
  static char id[CONTROL_ID_LENGTH];
  int r = rand();
  snprintf(id, CONTROL_ID_LENGTH, "%s%d", hl7_generate_date_time(), r);
  return id;
}
