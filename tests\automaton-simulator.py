#!/usr/bin/python3

import argparse
import os
import socket
import sys
from dataclasses import dataclass

RECEIVE_STR = "RECEIVE"

# ASCII values for delimiters
VT = 0x0B  # <VT> Vertical Tab
FS = 0x1C  # <FS> File Separator
CR = 0x1D  # <FS> File Separator


@dataclass
class Order:
    message_file: str
    receive_after: int


def read_config(config_file):
    config = {}
    with open(config_file, "r") as file:
        for line in file:
            key, value = line.strip().split("=")
            config[key.strip()] = value.strip()
    return config


def wrap_message_with_vt_fs(message: bytes) -> bytes:
    """Wrap a message with <VT> at the start and <FS> at the end."""

    return bytes([VT]) + message + bytes([FS])


def replace_with_cr(message: bytes):
    """Replace all newlines characters with carriage returns."""

    return message.replace(b"\n", b"\r")


def extract_messages(buffer: bytearray) -> list[bytes]:
    """Extract complete messages wrapped with <VT> and <FS>."""
    messages = []
    while True:
        start_idx = buffer.find(bytes([VT]))
        end_idx = buffer.find(bytes([FS]))

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            # Extract the message and remove it from the buffer
            message = buffer[start_idx + 1 : end_idx]  # Remove <VT> and <FS>
            messages.append(message)
            del buffer[: end_idx + 1]  # Remove processed message from buffer
        else:
            # No complete message found, break
            break
    return messages


class ConnectionClosed(Exception):
    pass


class NotAllBytesSent(Exception):
    pass


def simulate_automaton(
    ip, port, recv_timeout, orders: list[Order], carriage_returns: bool = False
):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(float(recv_timeout))
        sock.connect((ip, port))
        print(f"Connected to {ip}:{port}")

        for order in orders:
            if not order.message_file:
                raise Exception("invalid order")

            print(f"Sending message from {order.message_file}...")
            buffer = bytearray()
            with open(order.message_file, "rb") as msg_file:
                message = msg_file.read()
                # Wrap the message with <VT>...<FS>
                wrapped_message = wrap_message_with_vt_fs(message)
                if carriage_returns:
                    wrapped_message = replace_with_cr(wrapped_message)
                buffer.extend(wrapped_message)
            n = sock.send(buffer)
            if n != len(buffer):
                raise NotAllBytesSent
            else:
                print("Done.")

            # Buffer to accumulate received data
            recv_buffer = bytearray()
            received_count = 0

            while received_count < order.receive_after:
                print("Waiting for incoming message...")
                # Receive data and accumulate it
                data = sock.recv(4096)
                if not data:
                    raise ConnectionClosed
                recv_buffer.extend(data)

                # Extract and process all complete messages in the buffer
                messages = extract_messages(recv_buffer)
                if not messages:
                    print("No complete messages in this chunk.")
                    continue
                for message in messages:
                    received_count += 1
                    try:
                        print("Received message : ")
                        print(message.decode())  # Attempt to decode as UTF-8
                    except UnicodeDecodeError:
                        print(f"Received non-text message: {message.hex()}")

    except ConnectionResetError:
        print("Unexpected : connection closed.")
    except ConnectionClosed:
        print("Connection closed by peer.")
    except NotAllBytesSent:
        print("Not all bytes could be sent.")
    except socket.timeout:
        print("No message received (timeout).")
    finally:
        print("Closing connection...", end="")
        sock.close()
        print("Done.")


def parse_orders(args):
    orders = []

    for i, arg in enumerate(args):
        if arg == RECEIVE_STR:
            continue
        else:
            # Check if the file exists and is readable
            if not os.path.isfile(arg):
                print(f"Error: File '{arg}' does not exist or is not a valid file.")
                return []

            receive_after = 0
            for arg1 in args[i + 1 :]:
                if arg1 == RECEIVE_STR:
                    receive_after += 1
                else:
                    break

            orders.append(Order(message_file=arg, receive_after=receive_after))
            receive_after = False

    return orders


if __name__ == "__main__":
    print(4)

    # Parse command-line arguments
    parser = argparse.ArgumentParser(
        description="Simulate automaton TCP communication."
    )
    parser.add_argument(
        "--config", required=True, help="Path to the TCP configuration file."
    )
    parser.add_argument(
        "--usecarriagereturns",
        action="store_true",
        help="Replace new linies by carriage returns in sent files. Useful for HL7 messages.",
    )
    parser.add_argument(
        "messages",
        nargs="+",
        help=f"List of message files to send, separated by {RECEIVE_STR} to wait for a response.",
    )

    try:
        args = parser.parse_args()
    except argparse.ArgumentError as e:
        print(f"Argument parsing error: {e}\n")
        parser.print_help()
        sys.exit(1)

    # Read the TCP configuration
    config = read_config(args.config)
    SERVER_IP = config.get("bind-ip", "127.0.0.1")
    SERVER_PORT = int(config.get("bind-port", 1234))
    RECV_TIMEOUT = config.get("recv-timeout", 120)

    # Parse the message orders
    orders = parse_orders(args.messages)

    if not orders:
        exit(1)

    # Simulate the automaton communication
    simulate_automaton(
        SERVER_IP,
        SERVER_PORT,
        RECV_TIMEOUT,
        orders,
        carriage_returns=args.usecarriagereturns,
    )
