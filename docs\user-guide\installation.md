# Installation

Ce guide vous accompagne dans l'installation des drivers d'automates Synlab sur votre système Linux RedHat.

## Prérequis système

### Outils de développement requis

Assurez-vous que ces outils sont installés sur votre système :

```bash
# Vérifier la présence des outils
which cmake
which make
which gcc
```

### Bibliothèques requises

Les drivers nécessitent plusieurs bibliothèques externes :

| Bibliothèque | Description | Installation |
|--------------|-------------|--------------|
| **libconfuse** | Lecture des fichiers de configuration | `yum install libconfuse-devel` |
| **cmake** | Système de build | `yum install cmake` |
| **libxml2** | Parseur XML | `yum install libxml2-devel` |
| **libserialport** | Communication série RS232 | Compilation manuelle requise |

### Installation des dépendances RedHat

```bash
# Installer les paquets disponibles via yum
sudo yum update
sudo yum install cmake gcc gcc-c++ make
sudo yum install libconfuse-devel libxml2-devel

# Pour le développement (optionnel)
sudo yum install git clang-format cppcheck
```

### Installation de libserialport

libserialport n'est pas disponible dans les dépôts RedHat standard. Installation manuelle :

```bash
# Télécharger et compiler libserialport
wget https://sigrok.org/download/source/libserialport/libserialport-0.1.1.tar.gz
tar -xzf libserialport-0.1.1.tar.gz
cd libserialport-0.1.1
./configure
make
sudo make install
sudo ldconfig
```

## Compilation des drivers

### 1. Récupération du code source

```bash
# Cloner le dépôt (si applicable)
git clone [URL_DU_DEPOT]
cd drivers_automates

# Ou extraire l'archive
tar -xzf drivers_automates.tar.gz
cd drivers_automates
```

### 2. Configuration et compilation

```bash
# Créer le répertoire de build
mkdir build && cd build

# Configurer avec CMake
cmake ..

# Compiler
make

# Vérifier la compilation
ls -la bin/
```

### 3. Vérification de l'installation

```bash
# Tester un driver (exemple F200)
./bin/f200 --help

# Vérifier les dépendances
ldd ./bin/f200
```

## Structure après installation

Après compilation, vous devriez avoir cette structure :

```
build/
├── bin/                    # Exécutables des drivers
│   ├── f200               # Driver F200
│   ├── [autres_drivers]   # Autres drivers compilés
│   └── ...
├── lib/                   # Bibliothèques internes
└── ...
```

## Résolution de problèmes

### Erreur : "libconfuse not found"

```bash
# Vérifier l'installation
pkg-config --cflags --libs libconfuse

# Si absent, réinstaller
sudo yum install libconfuse-devel
```

### Erreur : "libserialport not found"

```bash
# Vérifier l'installation
pkg-config --cflags --libs libserialport

# Si absent, recompiler libserialport
export PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH
```

### Erreur de compilation CMake

```bash
# Nettoyer et recommencer
rm -rf build/
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Debug
make VERBOSE=1
```

### Permissions pour les ports série

```bash
# Ajouter l'utilisateur au groupe dialout
sudo usermod -a -G dialout $USER

# Redémarrer la session ou :
newgrp dialout

# Vérifier les permissions
ls -la /dev/ttyS* /dev/ttyUSB*
```

## Installation en production

### Copie des binaires

```bash
# Créer un répertoire d'installation
sudo mkdir -p /opt/synlab/drivers
sudo cp build/bin/* /opt/synlab/drivers/

# Créer des liens symboliques (optionnel)
sudo ln -s /opt/synlab/drivers/f200 /usr/local/bin/f200
```

### Configuration des logs

```bash
# Créer les répertoires de logs
sudo mkdir -p /var/log/synlab
sudo chown $USER:$USER /var/log/synlab
```

### Service systemd (optionnel)

Pour lancer automatiquement un driver au démarrage :

```bash
# Créer un fichier service
sudo tee /etc/systemd/system/synlab-f200.service << EOF
[Unit]
Description=Synlab F200 Driver
After=network.target

[Service]
Type=simple
User=synlab
WorkingDirectory=/opt/synlab/drivers
ExecStart=/opt/synlab/drivers/f200 -c /etc/synlab/f200.config
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Activer le service
sudo systemctl enable synlab-f200.service
```

## Prochaines étapes

Une fois l'installation terminée :

1. [**Configuration**](configuration.md) - Créer vos fichiers de configuration
2. [**Premier lancement**](quickstart.md) - Tester votre installation
3. [**Utilisation**](usage.md) - Utiliser les drivers au quotidien

!!! success "Installation réussie"
    Si tous les drivers se compilent sans erreur et que `./bin/f200 --help` fonctionne, votre installation est prête !
