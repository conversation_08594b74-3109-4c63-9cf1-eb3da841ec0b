#include "input_output.h"

#include "log.h"
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <unistd.h>

#define INITIAL_DATA_CAPACITY 1024

int io_data_init(Data *destination) {
  LOG_CHECK_MALLOC(destination->data,
                   malloc(INITIAL_DATA_CAPACITY * sizeof(unsigned char)));

  destination->capacity = INITIAL_DATA_CAPACITY;
  destination->length = 0;

  return 0;
}

void io_data_dispose(Data *data) {
  if (!data)
    return;
  if (!data->data)
    return;
  free(data->data);
}

int io_data_append(Data *destination, const unsigned char *data,
                   const size_t length) {
  if (destination->length + length >= destination->capacity) {
    while (destination->length + length >= destination->capacity) {
      size_t new_capacity = destination->capacity * 2;
      LOG_CHECK_MALLOC(
          destination->data,
          realloc(destination->data, sizeof(unsigned char) * new_capacity));
      destination->capacity = new_capacity;
    }
  }
  memcpy(destination->data + destination->length, data, length);
  destination->length += length;
  *(destination->data + destination->length) = 0;

  return 0;
}

int io_data_read_stdin(Data *data) {
  unsigned char buffer[INITIAL_DATA_CAPACITY];

  while (1) {
    ssize_t count = read(0, buffer, sizeof(buffer));
    if (count == -1) {
      last_error_set("can't read from stdin");
      return 1;
    }
    if (count == 0)
      break;
    if (io_data_append(data, buffer, (size_t)count))
      return 1;
  }

  return 0;
}

int io_data_read_file(Data *data, const char *path) {
  unsigned char buffer[INITIAL_DATA_CAPACITY];
  int fd = open(path, O_RDONLY);
  if (-1 == fd) {
    last_error_set("open(\"%s\") failed", path);
    return 1;
  }

  while (1) {
    ssize_t count = read(fd, buffer, sizeof(buffer));
    if (-1 == count) {
      last_error_set("read the file %", path);
      return 1;
    }
    if (0 == count)
      break;
    if (io_data_append(data, buffer, (size_t)count)) {
      last_error_set("cannot append data (from file %s)", path);
      return 1;
    }
  }

  close(fd);
  return 0;
}