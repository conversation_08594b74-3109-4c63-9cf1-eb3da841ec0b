# Guide d'implémentation pour les drivers d'automates Synlab

Ce guide détaille les étapes pour implémenter un nouveau protocole ou appareil dans le système de drivers d'automates Synlab.

## Table des matières

1. [Vue d'ensemble de l'architecture](#1-vue-densemble-de-larchitecture)
2. [Implémentation d'un nouveau protocole de transport](#2-implémentation-dun-nouveau-protocole-de-transport)
3. [Implémentation d'un nouveau protocole d'application](#3-implémentation-dun-nouveau-protocole-dapplication)
4. [Implémentation d'un nouveau système de stockage](#4-implémentation-dun-nouveau-système-de-stockage)
5. [Intégration d'un nouvel automate](#5-intégration-dun-nouvel-automate)
6. [Tests et validation](#6-tests-et-validation)

## 1. Vue d'ensemble de l'architecture

Le système est divisé en trois couches indépendantes qui communiquent via un modèle publish/subscribe :

- **Couche de transport** : Gère la communication physique (TCP/IP, RS232, etc.)
- **Couche d'application** : Interprète les protocoles (HL7, POCT, etc.)
- **Couche de stockage** : Sauvegarde les données traitées

Chaque couche expose une interface standard permettant l'interopérabilité.

## 2. Implémentation d'un nouveau protocole de transport

### 2.1 Structure des fichiers

Créez les fichiers suivants dans `src/transport_layer/[nouveau_protocole]/` :

```
├── [nouveau_protocole]_config.h
├── [nouveau_protocole]_config.c
├── [nouveau_protocole]_config_file.h
├── [nouveau_protocole]_config_file.c
├── [nouveau_protocole]_layer.h
└── [nouveau_protocole]_layer.c
```

### 2.2 Implémentation de l'interface

Dans `[nouveau_protocole]_layer.h`, définissez l'interface standard :

```c
#pragma once

#include "pubsub.h"
#include "[nouveau_protocole]_config.h"

int transport_layer_init(TransportConfiguration *init_config,
                         PubSub *init_transport2application,
                         PubSub *init_application2transport);

int transport_layer_read_publish_loop();

void transport_layer_send(void *void_context, const void *void_data);

void transport_layer_end();
```

### 2.3 Configuration

Définissez la structure de configuration dans `[nouveau_protocole]_config.h` :

```c
#pragma once

// Définir les clés de configuration
#define CONFIG_KEY1 "key1"
#define CONFIG_KEY2 "key2"

// Valeurs par défaut
#define INI_VALUE1 -1
#define INI_VALUE2 -1

typedef struct {
    // Paramètres spécifiques au protocole
    char *param1;
    int param2;
} TransportConfiguration;

void transport_configuration_init(TransportConfiguration *config);
void transport_configuration_destruct(TransportConfiguration *config);
char *transport_configuration_to_string(const TransportConfiguration *config);
```

### 2.4 Mise à jour du CMakeLists.txt

Ajoutez le nouveau protocole dans `src/CMakeLists.txt` :

```cmake
file(GLOB_RECURSE NOUVEAU_PROTOCOLE_LAYER_SOURCES
     "${CMAKE_CURRENT_SOURCE_DIR}/transport_layer/[nouveau_protocole]/*.c")

# Ajouter à la liste des cibles
add_executable(driver_nouveau_protocole ${MAIN_SOURCES} 
               ${NOUVEAU_PROTOCOLE_LAYER_SOURCES}
               ${HL7_LAYER_SOURCES}
               ${FILE_STORING_LAYER_SOURCES})
target_compile_definitions(driver_nouveau_protocole PRIVATE NOUVEAU_PROTOCOLE HL7 FILE_STORING)
target_link_libraries(driver_nouveau_protocole ${MAIN_LIBRARIES} hl7_260 [autres_dépendances])
```

## 3. Implémentation d'un nouveau protocole d'application

### 3.1 Structure des fichiers

Créez les fichiers suivants dans `src/application_layer/[nouveau_protocole]/` :

```
├── [nouveau_protocole]_config.h
├── [nouveau_protocole]_config.c
├── [nouveau_protocole]_config_file.h
├── [nouveau_protocole]_config_file.c
├── [nouveau_protocole]_layer.h
└── [nouveau_protocole]_layer.c
```

### 3.2 Implémentation de l'interface

Dans `[nouveau_protocole]_layer.h`, définissez l'interface standard :

```c
#pragma once

#include "[nouveau_protocole]_config.h"
#include "pubsub.h"

int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing);

void application_layer_process_message(void *void_context,
                                       const void *void_data);

void application_layer_end();
```

### 3.3 Bibliothèque de parsing

Si nécessaire, créez une bibliothèque de parsing dans `lib/[nouveau_protocole]/` :

```
lib/[nouveau_protocole]/
├── include/
│   ├── message.h
│   ├── common.h
│   └── ...
├── src/
│   ├── message.c
│   └── ...
└── CMakeLists.txt
```

## 4. Implémentation d'un nouveau système de stockage

### 4.1 Structure des fichiers

Créez les fichiers suivants dans `src/storing_layer/[nouveau_stockage]/` :

```
├── [nouveau_stockage]_config.h
├── [nouveau_stockage]_config.c
├── [nouveau_stockage]_config_file.h
├── [nouveau_stockage]_config_file.c
├── [nouveau_stockage]_layer.h
└── [nouveau_stockage]_layer.c
```

### 4.2 Implémentation de l'interface

Dans `[nouveau_stockage]_layer.h`, définissez l'interface standard :

```c
#pragma once

#include "[nouveau_stockage]_config.h"
#include "pubsub.h"

int storing_layer_init(StoringConfiguration *init_config,
                       PubSub *init_application2storing);

void storing_layer_store_message(void *void_context, const void *void_data);

void storing_layer_end();
```

## 5. Intégration d'un nouvel automate

### 5.1 Pour un automate utilisant un protocole existant

Si l'automate utilise un protocole déjà implémenté (ex: HL7, POCT), ajoutez simplement les spécifications spécifiques à l'automate :

1. Pour HL7 : Ajoutez les segments spécifiques dans `lib/hl7/src/segments_specifications/`
2. Pour POCT : Créez un nouveau driver dans `src/application_layer/poct/drivers/`

### 5.2 Pour un automate avec un nouveau protocole

Suivez les étapes des sections 2, 3 et 4 pour implémenter les couches nécessaires.

## 6. Tests et validation

### 6.1 Création de tests unitaires

Ajoutez des tests unitaires pour chaque composant dans le dossier `tests/`.

### 6.2 Création d'un simulateur

Créez un simulateur pour tester l'intégration dans `tests/simulators/` :

```python
#!/usr/bin/python3

import argparse
import socket
import sys
import time

def simulate_device(config_file, message_files):
    # Implémentation du simulateur
    pass

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Simulate [nouveau_protocole] communication."
    )
    parser.add_argument(
        "--config", required=True, help="Path to the configuration file."
    )
    parser.add_argument(
        "messages",
        nargs="+",
        help="List of message files to send."
    )
    
    args = parser.parse_args()
    simulate_device(args.config, args.messages)
```

### 6.3 Ajout au script de test

Mettez à jour `tests/tester.py` pour inclure les tests du nouveau protocole ou automate.

---

Ce guide fournit les étapes essentielles pour étendre le système avec de nouveaux protocoles et automates. Pour une assistance plus détaillée sur une implémentation spécifique, consultez les exemples existants dans le code source.