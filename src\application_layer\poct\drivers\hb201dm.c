#include "hb201dm.h"

#include "log.h"
#include "poct_utils.h"
#include <libxml/parser.h>
#include <libxml/tree.h>
#include <stdbool.h>
#include <stdlib.h>

/////////////////////////////////////////
// MESSAGES AND TOPICS
/////////////////////////////////////////

enum {
  POCTTopic<PERSON><PERSON>,
  POCTTopicStatus,
  POCTTopicObservations,
  POCTTopicGoodbye,
};

enum {
  POCTMessageHello,
  POCTMessageAcknowledge,
  POCTMessageStatus,
  POCTMessageRequest,
  POCTMessagePatientObservation,
  POCTMessageQualityControlObservation,
  POCTMessageEvents,
  POCTMessageEndOfTopic,
};

static const POCTTopicType topics[] = {
    POCTTop<PERSON><PERSON><PERSON>,
    POCTTopicStatus,
    POCTTopicObservations,
    POCTTopicGoodbye,
};

static const char *topics_names[] = {
    "",
    "",
    "",
    "",
};

static const POCTTopicType messages[] = {POCTMessageHello,
                                         POCTMessageAcknowledge,
                                         POCTMessageStatus,
                                         POCTMessageRequest,
                                         POCTMessagePatientObservation,
                                         POCTMessageQualityControlObservation,
                                         POCTMessageEvents,
                                         POCTMessageEndOfTopic,
                                         POCTMessageRequest};

static const char *messages_names[] = {"HEL.R01", "ACK.R01", "DST.R01",
                                       "REQ.R01", "OBS.R01", "OBS.R02",
                                       "EVS",     "EOT.R01", "DTV.R01"};

const PoctSpecification poct_specification = {.messages = messages,
                                              .messages_length = 9,
                                              .messages_strings =
                                                  messages_names,
                                              .topics = topics,
                                              .topics_length = 4,
                                              .topics_strings = topics_names};

/////////////////////////////////////////
// CONTEXT
/////////////////////////////////////////

typedef enum {
  StepInit,
  StepStatus,
  StepObservations,
  StepEvents,
} Step;

typedef struct ContextStruct {
  Step current_step;
  char *current_conversation;
  char *vendor_id;
  char *device_id;
  size_t observations_to_retrieve;
  size_t events_to_retrieve;
  size_t message_count;
} Context;

static Context *context_create() {
  Context *context;
  LOG_CHECK_MALLOC(context, malloc(sizeof(Context)));

  context->current_step = StepInit;
  context->current_conversation = NULL;
  context->vendor_id = NULL;
  context->device_id = NULL;
  context->message_count = 0;
  context->observations_to_retrieve = 0;
  context->events_to_retrieve = 0;

  return context;
}

static void context_destruct(Context *context) {
  if (!context)
    return;

  if (context->current_conversation)
    free(context->current_conversation);

  if (context->vendor_id)
    free(context->vendor_id);

  if (context->device_id)
    free(context->device_id);

  free(context);
}

static void context_reset(Context *context) {
  if (context->current_conversation)
    free(context->current_conversation);
  if (context->current_conversation)
    free(context->current_conversation);
  context->current_conversation = NULL;
  context->current_step = StepInit;
  if (context->device_id)
    free(context->device_id);
  if (context->vendor_id)
    free(context->vendor_id);
}

const char *poct_conversation_name(POCTConversationHandler *pch) {
  Context *context = (Context *)pch->context;
  if (!context->current_conversation)
    context->current_conversation = strdup(poct_generate_iso8601_timestamp());

  return context->current_conversation;
}

const char *poct_message_name(POCTConversationHandler *pch) {
  Context *context = (Context *)pch->context;
  static char name[10];
  snprintf(name, 10, "%zu", context->message_count);

  return name;
}

/////////////////////////////////////////
// HANDLERS
/////////////////////////////////////////

// helpers

static void stop_conversation_on_error(const POCTMessage *received_message,
                                       Context *context,
                                       POCTMessageHandlingResult *result) {

  context_reset(context);
  result->response = generate_response_list(
      1, generate_acknowledgment(false, received_message, &poct_specification));
}

#define NEED_NODE(root, path, goto_flag)                                       \
  if (!get_node_by_path(root, path)) {                                         \
    last_error_display(LOG_WARN, "xml node not found");                        \
    status = 1;                                                                \
    goto goto_flag;                                                            \
  }

#define NEED_ATTRIBUTE(root, path, attr_name, variable, goto_flag)             \
  variable = NULL;                                                             \
  variable = get_attribute_value_by_path(root, path, attr_name);               \
  if (!variable) {                                                             \
    last_error_display(LOG_WARN, "xml attribute not found");                   \
    status = 1;                                                                \
    goto goto_flag;                                                            \
  }

// hello

static void hello_handler(const POCTMessage *message, Context *context,
                          POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  NEED_ATTRIBUTE(root, "DEV/DEV.device_id", "V", context->device_id,
                 hello_handler_clean);
  NEED_ATTRIBUTE(root, "DEV/DEV.vendor_id", "V", context->vendor_id,
                 hello_handler_clean);

  log_debug("starting new conversation with device id %s, vendor id %s",
            context->device_id, context->vendor_id);

  // context
  context->current_step = StepStatus;

  // response
  result->message_is_valid = true;
  result->response = generate_response_list(
      1, generate_acknowledgment(true, message, &poct_specification));

hello_handler_clean:
  if (status)
    stop_conversation_on_error(message, context, result);
}

// status

static void status_handler(const POCTMessage *message, Context *context,
                           POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  char *str;
  NEED_ATTRIBUTE(root, "DST/DST.new_observations_qty", "V", str,
                 status_handler_clean);
  context->observations_to_retrieve = atoi(str);
  free(str);
  NEED_ATTRIBUTE(root, "DST/DST.new_events_qty", "V", str,
                 status_handler_clean);
  context->events_to_retrieve = atoi(str);

  log_info("retrieving %zu observations and %zu events",
           context->observations_to_retrieve, context->events_to_retrieve);

  // context
  context->current_step = StepObservations;

  // response
  result->message_is_valid = true;
  result->response = generate_response_list(
      2, generate_acknowledgment(true, message, &poct_specification),
      generate_document_with_children("REQ.R01", &poct_specification, 1,
                                      "REQ/REQ.request_cd", "V", "ROBS"));

status_handler_clean:
  if (status)
    stop_conversation_on_error(message, context, result);
  if (str)
    free(str);
}

// observations

static void observations_handler(const POCTMessage *message, Context *context,
                                 POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  char *str;

  if (message->type == POCTMessagePatientObservation ||
      message->type == POCTMessageQualityControlObservation) {
    // continue to receive
    if (message->type == POCTMessagePatientObservation) {
      NEED_ATTRIBUTE(root, "SVC/PT/PT.patient_id", "V", str,
                     observations_handler_clean);
      log_info("received patient observations");
    } else if (message->type == POCTMessageQualityControlObservation) {
      NEED_ATTRIBUTE(root, "SVC/CTC/CTC.name", "V", str,
                     observations_handler_clean);
      log_info("received quality control observations");
    }

    context->current_step = StepObservations;
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, &poct_specification));
  } else if (message->type == POCTMessageEndOfTopic) {
    // stop receiving
    context->current_step = StepEvents;
    result->message_is_valid = true;
    result->response = generate_response_list(
        2, generate_acknowledgment(true, message, &poct_specification),
        generate_document_with_children("REQ.R01", &poct_specification, 1,
                                        "REQ/REQ.request_cd", "V", "RDEV"));
  }

observations_handler_clean:
  if (str)
    free(str);
  if (status)
    stop_conversation_on_error(message, context, result);
}

// observations

static void events_handler(const POCTMessage *message, Context *context,
                           POCTMessageHandlingResult *result) {
  int status = 0;

  // needed nodes
  xmlNodePtr root = xmlDocGetRootElement(message->xml_doc);
  char *str;

  if (message->type == POCTMessageEvents) {
    // continue to receive
    NEED_ATTRIBUTE(root, "SVC/EVT/EVT.description", "V", str,
                   observations_handler_clean);
    log_info("received events");

    context->current_step = StepEvents;
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_acknowledgment(true, message, &poct_specification));
  } else if (message->type == POCTMessageEndOfTopic) {
    // end of conversation
    result->message_is_valid = true;
    result->response = generate_response_list(
        1, generate_document_with_children("DTV.R01", &poct_specification, 1,
                                           "DTV/DTV.command_cd", "V",
                                           "START_CONTINUOUS"));
    context_reset(context);
  }

observations_handler_clean:
  if (str)
    free(str);
  if (status)
    stop_conversation_on_error(message, context, result);
}

// main handler

typedef void (*MessageHandler)(const POCTMessage *, Context *,
                               POCTMessageHandlingResult *);

static const MessageHandler handlers[] = {
    [StepInit] = hello_handler,
    [StepStatus] = status_handler,
    [StepObservations] = observations_handler,
    [StepEvents] = events_handler,
};

static const POCTMessageType handlers_expected_messages[][10] = {
    [StepInit] = {POCTMessageHello, -1},
    [StepStatus] = {POCTMessageStatus, -1},
    [StepObservations] = {POCTMessagePatientObservation,
                          POCTMessageQualityControlObservation,
                          POCTMessageEndOfTopic, -1},
    [StepEvents] = {POCTMessageEvents, POCTMessageEndOfTopic, -1}};

static POCTMessageHandlingResult *handler(const POCTMessage *message,
                                          void *context_void) {
  if (!message) {
    log_error("can't handle a NULL message");
    return NULL;
  }

  (void)message;
  Context *context = (Context *)context_void;

  POCTMessageHandlingResult *result =
      (POCTMessageHandlingResult *)malloc(sizeof(POCTMessageHandlingResult));

  result->message_is_valid = false;
  result->conversation_name = context->current_conversation;
  result->response = NULL;

  bool message_has_good_type = false;
  for (const POCTMessageType *ptr =
           handlers_expected_messages[context->current_step];
       *ptr != -1; ptr++) {
    if (*ptr == message->type) {
      message_has_good_type = true;
      break;
    }
  }

  if (!message_has_good_type) {
    log_error("wrong message type");
    stop_conversation_on_error(message, context, result);
    return result;
  }

  handlers[context->current_step](message, context, result);
  context->message_count++;

  return result;
}

/////////////////////////////////////////
// POCT CONVERSATION HANDLER
/////////////////////////////////////////

POCTConversationHandler *poct_conversation_handler_create() {
  POCTConversationHandler *pch;
  LOG_CHECK_MALLOC(pch, malloc(sizeof(POCTConversationHandler)));

  Context *context = context_create();
  if (!context) {
    poct_conversation_handler_destruct(pch);
    return NULL;
  }
  context->current_conversation = NULL;

  pch->context = context;
  pch->handler = handler;

  return pch;
}

void poct_conversation_handler_destruct(POCTConversationHandler *pch) {
  if (!pch)
    return;

  if (pch->context)
    context_destruct(pch->context);

  free(pch);
}