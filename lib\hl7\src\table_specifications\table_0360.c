#include "table_0360.h"

#if HL7_VERSION == 240

const char *Table0360Values[] = {
    "AA", "AAS", "ABA", "AE",  "AS",     "BA",  "BBA", "BE",  "BFA", "BN",
    "BS", "BSL", "BT",  "CER", "DBA",    "DED", "DIP", "DO",  "HS",  "JD",
    "MA", "MBA", "MCE", "MD",  "MDI",    "ME",  "MED", "MEE", "MFA", "MME",
    "MS", "MSL", "MT",  "NG",  "PharmD", "PHD", "PHE", "PHS", "SEC", "TS",
};

const HL7Table Table0360 = {.table_name = "degree/license/certificate",
                            .valid_values = Table0360Values,
                            .value_count = 40};

#elif HL7_VERSION == 250

const char *Table0360Values[] = {
    "AA",     "AAS", "ABA", "AE",  "AS",   "BA",  "BBA", "BE",   "BFA",  "BN",
    "BS",     "BSL", "BSN", "BT",  "CANP", "CER", "CMA", "CNM",  "CNP",  "CNS",
    "CPNP",   "CRN", "DBA", "DED", "DIP",  "DO",  "EMT", "EMTP", "FPNP", "HS",
    "JD",     "MA",  "MBA", "MCE", "MD",   "MDA", "MDI", "ME",   "MED",  "MEE",
    "MFA",    "MME", "MS",  "MSL", "MSN",  "MT",  "MT",  "NG",   "NP",   "PA",
    "PharmD", "PHD", "PHE", "PHS", "PN",   "RMA", "RPH", "SEC",  "TS",
};

const HL7Table Table0360 = {.table_name = "degree/license/certificate",
                            .valid_values = Table0360Values,
                            .value_count = 59};

#elif HL7_VERSION == 260

const char *Table0360Values[] = {
    "AA",     "AAS", "ABA", "AE",  "AS",   "BA",  "BBA", "BE",   "BFA",  "BN",
    "BS",     "BSL", "BSN", "BT",  "CANP", "CER", "CMA", "CNM",  "CNP",  "CNS",
    "CPNP",   "CRN", "DBA", "DED", "DIP",  "DO",  "EMT", "EMTP", "FPNP", "HS",
    "JD",     "MA",  "MBA", "MCE", "MD",   "MDA", "MDI", "ME",   "MED",  "MEE",
    "MFA",    "MME", "MS",  "MSL", "MSN",  "MT",  "MT",  "NG",   "NP",   "PA",
    "PharmD", "PHD", "PHE", "PHS", "PN",   "RMA", "RPH", "SEC",  "TS",
};

const HL7Table Table0360 = {.table_name = "degree/license/certificate",
                            .valid_values = Table0360Values,
                            .value_count = 59};

#endif
