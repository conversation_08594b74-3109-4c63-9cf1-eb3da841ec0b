/**
 * @file input_output.h
 * @brief Dynamic data manipulation.
 */

#pragma once

#include <stddef.h>

typedef struct {
  size_t capacity;
  size_t length;
  unsigned char *data;
} Data;

/**
 * @brief Initializes a data object.
 *
 * @param
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int io_data_init(Data *destination);

/**
 * @brief Frees a data object.
 *
 * @param
 */
void io_data_dispose(Data *data);

/**
 * @brief Append data dynamically.
 *
 * @param destination data object to append data to
 * @param data data to append
 * @param length length of data
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int io_data_append(Data *destination, const unsigned char *data,
                   const size_t length);

/**
 * @brief Reads from standard input.
 *
 * @warning The memory area used for storing data must be initialized
 * before calling this function (via a call to the `io_data_init` function).
 *
 * @param data Address of the memory area used for storing data
 * read from standard input.
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int io_data_read_stdin(Data *data);

/**
 * @brief Loads an entire file.
 *
 * @warning The memory area used for storing data must be initialized
 * before calling this function (via a call to the `io_data_init` function).
 *
 * @param data Address of the memory area used for storing data
 * read from the file.
 * @param path Path to the file to load.
 *
 * @return
 * - Success : 0
 * - Error : 1
 *
 * @todo Verify the use of the function (return value).
 */
int io_data_read_file(Data *data, const char *path);
