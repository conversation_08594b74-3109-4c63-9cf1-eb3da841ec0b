# Utilisation des drivers

Ce guide détaille l'utilisation quotidienne des drivers d'automates Synlab.

## Commande de base

Tous les drivers utilisent la même syntaxe :

```bash
./mon_driver -c fichier_configuration.config
```

### Options disponibles

| Option | Description | Exemple |
|--------|-------------|---------|
| `-c, --config` | Fichier de configuration | `-c ma_config.config` |
| `-h, --help` | Afficher l'aide | `-h` |
| `-v, --version` | Version du driver | `-v` |

## Modes de fonctionnement

### Mode interactif (par défaut)

Le driver s'exécute au premier plan et affiche les logs :

```bash
./bin/f200 -c f200.config
```

**Avantages :**
- Logs visibles en temps réel
- Arrêt facile avec Ctrl+C
- Idéal pour les tests et le debug

### Mode démon

Le driver s'exécute en arrière-plan :

```ini
[global]
daemonize = true
log-file = "/var/log/synlab/f200.log"
```

```bash
./bin/f200 -c f200.config
```

**Avantages :**
- Fonctionne en arrière-plan
- Survit à la fermeture du terminal
- Idéal pour la production

## Gestion des logs

### Niveaux de log

| Niveau | Description | Usage |
|--------|-------------|-------|
| `DEBUG` | Très détaillé | Développement, debug |
| `INFO` | Informations générales | Production normale |
| `WARN` | Avertissements | Production, surveillance |
| `ERROR` | Erreurs uniquement | Production critique |

### Configuration des logs

```ini
[global]
log-level = "INFO"
log-file = "./logs/driver.log"
```

### Rotation des logs

Pour éviter que les logs deviennent trop volumineux :

```bash
# Utiliser logrotate (recommandé)
sudo tee /etc/logrotate.d/synlab << EOF
/var/log/synlab/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 synlab synlab
}
EOF
```

## Surveillance et monitoring

### Vérifier qu'un driver fonctionne

```bash
# Vérifier le processus
ps aux | grep f200

# Vérifier l'écoute réseau (TCP)
netstat -tlnp | grep 1234

# Vérifier les logs récents
tail -f /var/log/synlab/f200.log
```

### Statistiques de fonctionnement

Les drivers loggent des statistiques utiles :

```bash
# Messages traités
grep "Message processed" /var/log/synlab/f200.log | wc -l

# Erreurs de connexion
grep -i "connection error" /var/log/synlab/f200.log

# Dernière activité
tail -1 /var/log/synlab/f200.log
```

## Gestion des erreurs

### Erreurs courantes et solutions

#### Erreur de configuration

```
ERROR: Invalid configuration parameter 'bind-port'
```

**Solution :** Vérifier la syntaxe du fichier de configuration

#### Erreur de connexion

```
ERROR: Cannot bind to port 1234: Address already in use
```

**Solutions :**
```bash
# Trouver qui utilise le port
sudo netstat -tlnp | grep 1234

# Changer le port dans la configuration
# ou arrêter l'autre processus
```

#### Erreur de permissions

```
ERROR: Cannot open serial port /dev/ttyUSB0: Permission denied
```

**Solution :**
```bash
sudo usermod -a -G dialout $USER
newgrp dialout
```

### Redémarrage automatique

En cas d'erreur critique, le driver s'arrête. Pour un redémarrage automatique :

#### Avec systemd

```bash
# Créer un service systemd
sudo tee /etc/systemd/system/synlab-f200.service << EOF
[Unit]
Description=Synlab F200 Driver
After=network.target

[Service]
Type=simple
User=synlab
WorkingDirectory=/opt/synlab
ExecStart=/opt/synlab/bin/f200 -c /etc/synlab/f200.config
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable synlab-f200.service
sudo systemctl start synlab-f200.service
```

#### Avec un script de surveillance

```bash
#!/bin/bash
# watch_driver.sh

DRIVER="/opt/synlab/bin/f200"
CONFIG="/etc/synlab/f200.config"
PIDFILE="/var/run/synlab-f200.pid"

while true; do
    if ! pgrep -f "$DRIVER" > /dev/null; then
        echo "$(date): Driver not running, restarting..."
        $DRIVER -c $CONFIG &
        echo $! > $PIDFILE
    fi
    sleep 30
done
```

## Optimisation des performances

### Configuration réseau

Pour de gros volumes de données :

```ini
[tcp]
bind-ip = "0.0.0.0"
bind-port = 1234
recv-timeout = 300        # Augmenter le timeout
buffer-size = 8192        # Si supporté
```

### Configuration stockage

```ini
[storing]
output-directory = "/fast/storage/synlab"  # Stockage rapide
create-subdirs = true
batch-size = 100          # Si supporté
```

### Monitoring système

```bash
# Utilisation CPU/mémoire
top -p $(pgrep f200)

# I/O disque
iotop -p $(pgrep f200)

# Connexions réseau
ss -tuln | grep 1234
```

## Maintenance

### Sauvegarde des configurations

```bash
# Sauvegarder les configurations
tar -czf synlab-configs-$(date +%Y%m%d).tar.gz /etc/synlab/

# Sauvegarder les données
rsync -av /var/data/synlab/ /backup/synlab/
```

### Nettoyage des logs

```bash
# Nettoyer les anciens logs
find /var/log/synlab/ -name "*.log" -mtime +30 -delete

# Compresser les logs anciens
find /var/log/synlab/ -name "*.log" -mtime +7 -exec gzip {} \;
```

### Mise à jour des drivers

```bash
# Arrêter le driver
sudo systemctl stop synlab-f200

# Sauvegarder l'ancien
cp /opt/synlab/bin/f200 /opt/synlab/bin/f200.backup

# Installer le nouveau
cp build/bin/f200 /opt/synlab/bin/

# Redémarrer
sudo systemctl start synlab-f200

# Vérifier
sudo systemctl status synlab-f200
```

## Intégration avec d'autres systèmes

### Scripts de post-traitement

Traiter les données après réception :

```bash
#!/bin/bash
# post_process.sh

DATA_DIR="/var/data/synlab"
PROCESSED_DIR="/var/data/synlab/processed"

# Surveiller les nouveaux fichiers
inotifywait -m -e create $DATA_DIR --format '%w%f' | while read file; do
    echo "Processing $file"
    # Votre traitement ici
    process_hl7_file "$file"
    mv "$file" "$PROCESSED_DIR/"
done
```

### API REST (si disponible)

Certains drivers peuvent exposer une API :

```bash
# Vérifier le statut
curl http://localhost:8080/status

# Obtenir les statistiques
curl http://localhost:8080/stats
```

## Prochaines étapes

- [**Exemples concrets**](examples.md) - Cas d'usage réels
- [**Résolution de problèmes**](troubleshooting.md) - Guide de dépannage
- [**Configuration avancée**](config-reference.md) - Référence complète
