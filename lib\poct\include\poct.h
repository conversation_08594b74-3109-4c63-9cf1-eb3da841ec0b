/**
 * @file poct.h
 * @brief POCT message parsing library.
 */

#pragma once

#include "common.h"
#include "message.h"
#include <stdbool.h>

/// @brief Initializes POCT library.
void poct_start();

/**
 * @brief Generates a string representation of the current time
 *
 * The format is "YYYYMMDDHHMMSS-ZZZZ"
 *
 * @return a string representation of the current time
 */
const char *poct_generate_iso8601_timestamp();

/// @brief Frees ressources associated with POCT library.
void poct_end();

/// @brief Result from a poct message handler.
typedef struct POCTMessageHandlingResultStruct {
  /// @brief Flag indicating the message couldn't be processed by the handler.
  ///
  /// If this is true, `response` shall be NULL.
  bool message_is_valid;
  /// @brief Response to send to peer.
  ///
  /// A NULL terminated list of messages to send as response.
  POCTMessage **response;
  /// @brief Conversation the message is included in.
  ///
  /// This allows to group messages part of the same conversation.
  const char *conversation_name;
} POCTMessageHandlingResult;

/// @brief Handler for a POCT conversation.
typedef struct POCTConversationHandlerStruct {
  /// @brief Function to process a message once received.
  POCTMessageHandlingResult *(*handler)(const POCTMessage *message,
                                        void *context);
  /// @brief Arbitrary-typed object, to keep persistent data between handler
  /// calls.
  void *context;
} POCTConversationHandler;

void poct_handling_result_destruct(POCTMessageHandlingResult *result);
