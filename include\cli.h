#pragma once

#include "global_config.h"
#include <stdlib.h>

#define CLI_HELP "help"
#define CLI_HELP_SHORT "h"
#define CLI_CONFIG "config"
#define CLI_CONFIG_SHORT "c"

typedef struct {
  /**
   * @brief Unified configuration filepath.
   */
  char *config_file;
} CLIArguments;

int cli_parse(CLIArguments *cli_arguments, const int argc, const char **argv,
              const char *program);

void cli_destruct(CLIArguments *cli_arguments);