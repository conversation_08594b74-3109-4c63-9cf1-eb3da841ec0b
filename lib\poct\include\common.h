#pragma once

#include <stddef.h>

typedef int POCTTopicType;
typedef int POCTMessageType;

/**
 * @brief Defines available topics and fields for a POCT communication.
 *
 * Because POCT is a very broad standard, clients can define their own set of
 * possible topics and messages.
 */
typedef struct PoctSpecificationStruct {
  const POCTTopicType *topics;
  const char **topics_strings;
  const size_t topics_length;
  const POCTMessageType *messages;
  const char **messages_strings;
  const size_t messages_length;
} PoctSpecification;