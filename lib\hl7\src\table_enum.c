#include "table_enum.h"

#include "log.h"

#define GENERATE_CONVERSION(ENUM) {TableType##ENUM, #ENUM},

static const struct {
  TableType val;
  const char *str;
} string2st_conversion[] = {FOREACH_TABLE_TYPE(GENERATE_CONVERSION)};

TableType hl7_str2table_type(const char *str) {
  for (size_t j = 0;
       j < sizeof(string2st_conversion) / sizeof(string2st_conversion[0]); ++j)
    if (!strcmp(str, string2st_conversion[j].str))
      return string2st_conversion[j].val;

  last_error_set("unknown table type : %s", str);

  return TableTypeUnknown;
}

#define GENERATE_STRING(STRING) #STRING,

static const char *table_type_string[] = {FOREACH_TABLE_TYPE(GENERATE_STRING)};

const char *hl7_table_type2str(TableType type) {
  return table_type_string[type];
}