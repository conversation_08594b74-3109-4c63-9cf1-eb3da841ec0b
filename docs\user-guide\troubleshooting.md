# Résolution de problèmes

Ce guide vous aide à diagnostiquer et résoudre les problèmes courants avec les drivers d'automates Synlab.

## Diagnostic général

### Vérifications de base

Avant tout dépannage, effectuez ces vérifications :

```bash
# 1. Le driver est-il installé ?
ls -la /opt/synlab/bin/

# 2. Le fichier de configuration existe-t-il ?
ls -la /etc/synlab/ma_config.config

# 3. Les permissions sont-elles correctes ?
ls -la /var/log/synlab/

# 4. Y a-t-il des processus en cours ?
ps aux | grep synlab
```

### Logs de diagnostic

```bash
# Logs récents
tail -50 /var/log/synlab/driver.log

# Rechercher les erreurs
grep -i error /var/log/synlab/driver.log

# Logs en temps réel
tail -f /var/log/synlab/driver.log
```

## Problèmes de démarrage

### Le driver ne démarre pas

**Symptômes :**
- Aucun processus visible
- Pas de logs générés
- Retour immédiat à l'invite de commande

**Diagnostic :**
```bash
# Tester le driver manuellement
./bin/mon_driver -c ma_config.config

# Vérifier les dépendances
ldd ./bin/mon_driver
```

**Solutions courantes :**

#### Bibliothèque manquante
```bash
# Erreur : "libconfuse.so.2: cannot open shared object file"
sudo yum install libconfuse-devel

# Erreur : "libserialport.so.0: cannot open shared object file"  
sudo ldconfig
export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
```

#### Fichier de configuration invalide
```bash
# Vérifier la syntaxe
cat ma_config.config | grep -E "^\[|^[a-z]"

# Tester avec la configuration d'exemple
./bin/mon_driver -c tests/sample_configs/unified.config
```

### Erreur "Permission denied"

**Pour les ports série :**
```bash
# Vérifier les permissions
ls -la /dev/ttyUSB0

# Ajouter l'utilisateur au groupe dialout
sudo usermod -a -G dialout $USER
newgrp dialout

# Ou temporairement
sudo chmod 666 /dev/ttyUSB0
```

**Pour les fichiers de log :**
```bash
# Créer le répertoire avec les bonnes permissions
sudo mkdir -p /var/log/synlab
sudo chown $USER:$USER /var/log/synlab
```

## Problèmes de connexion

### TCP/IP : "Address already in use"

**Diagnostic :**
```bash
# Qui utilise le port ?
sudo netstat -tlnp | grep 1234
sudo ss -tlnp | grep 1234
```

**Solutions :**
```bash
# Option 1 : Arrêter l'autre processus
sudo kill [PID]

# Option 2 : Changer le port dans la configuration
# bind-port = 1235

# Option 3 : Attendre la libération (TIME_WAIT)
# Attendre 2-3 minutes
```

### TCP/IP : "Connection refused"

**Diagnostic :**
```bash
# Le driver écoute-t-il ?
netstat -tln | grep 1234

# Test de connexion locale
telnet 127.0.0.1 1234

# Test de connexion réseau
telnet ************* 1234
```

**Solutions :**
```bash
# Vérifier l'IP de bind
# bind-ip = "0.0.0.0"  # Toutes les interfaces
# bind-ip = "127.0.0.1"  # Local uniquement

# Vérifier le firewall
sudo iptables -L | grep 1234
sudo firewall-cmd --list-ports
```

### RS232 : "No such file or directory"

**Diagnostic :**
```bash
# Le port existe-t-il ?
ls -la /dev/ttyUSB* /dev/ttyS*

# Périphériques USB série
lsusb | grep -i serial
dmesg | grep tty
```

**Solutions :**
```bash
# Identifier le bon port
sudo dmesg | tail | grep tty

# Vérifier la connexion physique
# - Câble branché ?
# - Adaptateur USB reconnu ?
# - LED d'activité ?

# Tester avec un autre outil
sudo minicom -D /dev/ttyUSB0
```

## Problèmes de communication

### Aucun message reçu

**Diagnostic :**
```bash
# Le driver écoute-t-il ?
netstat -tln | grep [PORT]

# Y a-t-il du trafic réseau ?
sudo tcpdump -i any port [PORT]

# Logs de connexion
grep -i "connect" /var/log/synlab/driver.log
```

**Solutions :**
```bash
# Tester avec un client simple
echo "TEST" | nc [IP] [PORT]

# Vérifier la configuration de l'automate
# - Bonne IP de destination ?
# - Bon port ?
# - Protocole activé ?
```

### Messages malformés

**Symptômes dans les logs :**
```
ERROR: Invalid HL7 message format
WARN: Missing MSH segment
ERROR: Cannot parse message
```

**Diagnostic :**
```bash
# Examiner les messages bruts
hexdump -C /tmp/raw_message.txt

# Vérifier les caractères de fin
cat -A /tmp/raw_message.txt
```

**Solutions :**
- Vérifier l'encodage (UTF-8 vs ASCII)
- Contrôler les caractères de fin de ligne (\r\n vs \n)
- Valider la structure HL7

### Timeout de réception

**Symptômes :**
```
WARN: Receive timeout after 120 seconds
ERROR: Connection closed due to timeout
```

**Solutions :**
```ini
# Augmenter le timeout
[tcp]
recv-timeout = 300  # 5 minutes

# Ou pour RS232
[rs232]
recv-timeout = 600  # 10 minutes
```

## Problèmes de stockage

### Erreur "No space left on device"

**Diagnostic :**
```bash
# Espace disque
df -h /var/data/synlab

# Inodes disponibles
df -i /var/data/synlab

# Gros fichiers
du -sh /var/data/synlab/*
```

**Solutions :**
```bash
# Nettoyer les anciens fichiers
find /var/data/synlab -name "*.hl7" -mtime +30 -delete

# Compresser les anciens fichiers
find /var/data/synlab -name "*.hl7" -mtime +7 -exec gzip {} \;

# Déplacer vers un autre stockage
rsync -av /var/data/synlab/ /backup/synlab/
```

### Permissions d'écriture

**Erreur :**
```
ERROR: Cannot create file /var/data/synlab/message.hl7: Permission denied
```

**Solutions :**
```bash
# Vérifier les permissions
ls -la /var/data/synlab/

# Corriger les permissions
sudo chown -R synlab:synlab /var/data/synlab/
sudo chmod -R 755 /var/data/synlab/
```

## Problèmes de performance

### Driver lent ou qui rame

**Diagnostic :**
```bash
# Utilisation CPU/mémoire
top -p $(pgrep mon_driver)

# I/O disque
iotop -p $(pgrep mon_driver)

# Connexions réseau
ss -tuln | grep [PORT]
```

**Solutions :**
```bash
# Réduire le niveau de log
log-level = "ERROR"

# Optimiser le stockage
# - Utiliser un SSD
# - Stockage réseau rapide
# - Désactiver create-subdirs si non nécessaire

# Augmenter les timeouts
recv-timeout = 300
```

### Mémoire qui augmente (fuite mémoire)

**Diagnostic :**
```bash
# Surveiller la mémoire
watch -n 5 'ps aux | grep mon_driver'

# Logs de debug
log-level = "DEBUG"
```

**Solutions :**
```bash
# Redémarrer périodiquement (workaround)
# Crontab : 0 2 * * * systemctl restart synlab-driver

# Signaler le bug avec les logs de debug
```

## Outils de diagnostic

### Script de diagnostic automatique

```bash
#!/bin/bash
# diagnose.sh

echo "=== Diagnostic Synlab Driver ==="
echo "Date: $(date)"
echo

echo "1. Processus en cours:"
ps aux | grep -E "(synlab|driver)" | grep -v grep
echo

echo "2. Ports en écoute:"
netstat -tln | grep -E "(1234|1235|9999)"
echo

echo "3. Espace disque:"
df -h /var/log/synlab /var/data/synlab 2>/dev/null
echo

echo "4. Dernières erreurs:"
grep -i error /var/log/synlab/*.log 2>/dev/null | tail -5
echo

echo "5. Connexions réseau:"
ss -tuln | grep -E "(1234|1235)"
echo

echo "6. Ports série:"
ls -la /dev/ttyUSB* /dev/ttyS* 2>/dev/null
echo

echo "=== Fin du diagnostic ==="
```

### Test de connectivité

```bash
#!/bin/bash
# test_connectivity.sh

HOST=${1:-127.0.0.1}
PORT=${2:-1234}

echo "Test de connectivité vers $HOST:$PORT"

# Test TCP
if timeout 5 bash -c "</dev/tcp/$HOST/$PORT"; then
    echo "✅ Connexion TCP réussie"
else
    echo "❌ Connexion TCP échouée"
fi

# Test avec netcat
if echo "TEST" | timeout 5 nc $HOST $PORT; then
    echo "✅ Envoi de données réussi"
else
    echo "❌ Envoi de données échoué"
fi
```

## Obtenir de l'aide

### Informations à fournir

Lors d'une demande d'aide, fournissez :

1. **Version du driver**
   ```bash
   ./bin/mon_driver --version
   ```

2. **Configuration utilisée** (masquer les informations sensibles)

3. **Logs d'erreur complets**
   ```bash
   tail -100 /var/log/synlab/driver.log
   ```

4. **Environnement système**
   ```bash
   uname -a
   cat /etc/redhat-release
   ```

5. **Résultat du script de diagnostic**

### Logs de debug

Pour un diagnostic approfondi :

```ini
[global]
log-level = "DEBUG"
```

⚠️ **Attention :** Les logs DEBUG sont très verbeux et peuvent impacter les performances.

## Prochaines étapes

- [**FAQ**](faq.md) - Questions fréquentes
- [**Configuration**](config-reference.md) - Référence complète
- [**Exemples**](examples.md) - Configurations types
