#include "pid.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec PID_Spec[38] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient ID", FieldTypeCX, 20, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"patient identifier list", FieldTypeCX, 250, REQUIRED, REPEATABLE_INFINITE, TABLE_NONE},
    {"alternate patient ID",  FieldTypeCX, 20, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"patient name", FieldTypeXPN , 250, REQUIRED, REPEA<PERSON><PERSON>E_INFINITE, TABLE_NONE},
    {"mothers's maiden name", FieldTypeXPN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"date/time of birth", FieldTypeTS , 26, O<PERSON><PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"administrative sex", FieldTypeIS , 1, <PERSON><PERSON><PERSON><PERSON><PERSON>, NOT_REPEATABLE, TableType0001},
    {"patient alias", FieldTypeXPN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"race", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0005},
    {"patient address", FieldTypeXAD , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"county code", FieldTypeIS , 4, OPTIONAL, NOT_REPEATABLE, TableType0289},
    {"phone number - home", FieldTypeXTN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"phone number - business", FieldTypeXTN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"primary language", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0296},
    {"marital status", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0002},
    {"religion", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0006},
    {"patient account number", FieldTypeCX , 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"ssn number - patient", FieldTypeST , 16, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"driver license's - patient", FieldTypeDLN , 25, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"mother's identifier", FieldTypeCX , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ethnic group", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0189},
    {"birth place", FieldTypeST , 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"multiple birth indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"birth order", FieldTypeNM , 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"citizenship", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0171},
    {"veterans military status", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0172},
    {"nationality", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0212},
    {"patient death date and time", FieldTypeTS , 226, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient death indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"identity unknown indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"identity reliability code", FieldTypeIS , 20, OPTIONAL, REPEATABLE_INFINITE, TableType0445},
    {"last update date/time", FieldTypeTS , 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"last update facility", FieldTypeHD , 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"species code", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0446},
    {"breed code", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0447},
    {"strain", FieldTypeST , 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"production class code", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0429},
};
// clang-format on

#elif HL7_VERSION == 250 || HL7_VERSION == 260

// clang-format off
const FieldInSegmentSpec PID_Spec[39] = {
    {"set ID", FieldTypeSI, 4, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient ID", FieldTypeCX, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient identifier list", FieldTypeCX, 250, REQUIRED, REPEATABLE_INFINITE, TABLE_NONE},
    {"alternate patient ID",  FieldTypeCX, 20, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"patient name", FieldTypeXPN , 250, REQUIRED, REPEATABLE_INFINITE, TABLE_NONE},
    {"mothers's maiden name", FieldTypeXPN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"date/time of birth", FieldTypeTS , 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"administrative sex", FieldTypeIS , 1, OPTIONAL, NOT_REPEATABLE, TableType0001},
    {"patient alias", FieldTypeXPN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"race", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0005},
    {"patient address", FieldTypeXAD , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"county code", FieldTypeIS , 4, OPTIONAL, NOT_REPEATABLE, TableType0289},
    {"phone number - home", FieldTypeXTN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"phone number - business", FieldTypeXTN , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"primary language", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0296},
    {"marital status", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0002},
    {"religion", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0006},
    {"patient account number", FieldTypeCX , 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"ssn number - patient", FieldTypeST , 16, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"driver license's - patient", FieldTypeDLN , 25, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"mother's identifier", FieldTypeCX , 250, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"ethnic group", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0189},
    {"birth place", FieldTypeST , 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"multiple birth indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"birth order", FieldTypeNM , 2, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"citizenship", FieldTypeCE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0171},
    {"veterans military status", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0172},
    {"nationality", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0212},
    {"patient death date and time", FieldTypeTS , 226, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"patient death indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"identity unknown indicator", FieldTypeID , 1, OPTIONAL, NOT_REPEATABLE, TableType0136},
    {"identity reliability code", FieldTypeIS , 20, OPTIONAL, REPEATABLE_INFINITE, TableType0445},
    {"last update date/time", FieldTypeTS , 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"last update facility", FieldTypeHD , 40, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"species code", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0446},
    {"breed code", FieldTypeCE , 250, OPTIONAL, NOT_REPEATABLE, TableType0447},
    {"strain", FieldTypeST , 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"production class code", FieldTypeCE , 250, OPTIONAL, 2, TableType0429},
    {"tribal citizenship", FieldTypeCWE , 250, OPTIONAL, REPEATABLE_INFINITE, TableType0171},
};
// clang-format on

#endif