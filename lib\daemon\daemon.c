#include "daemon.h"
#include "log.h"
#include <fcntl.h>
#include <signal.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

/**
 * @brief Default signal handler for the daemon.
 *
 * @param sig Signal number.
 */
static void default_signal_handler(int sig) {
  if (sig == SIGHUP)
    return;
  else if (sig == SIGTERM)
    exit(0);
}

// this function uses double fork mechanism
// https://stackoverflow.com/a/5386753/13123535
// https://0xjet.github.io/3OHA/2022/04/11/post.html
int daemonize(const char *in_working_directory,
              void (*optional_signal_handler)(int)) {
  // Use default signal handler if none provided
  if (!optional_signal_handler)
    optional_signal_handler = default_signal_handler;

  // If the parent process ID is 1, we are already a daemon
  if (getppid() == 1)
    return 0;

  // First fork to create a background process
  pid_t pid = fork();
  if (pid < 0) {
    last_error_set("Can't enter daemon mode: fork() failed");
    return 1;
  }
  if (pid > 0) {
    // Exit parent process
    exit(0);
  }

  // Create a new session and become the session leader
  if (setsid() < 0) {
    last_error_set("Can't create new session: setsid() failed");
    return 1;
  }

  // Second fork to ensure the process cannot acquire a controlling terminal
  pid = fork();
  if (pid < 0) {
    last_error_set("Can't fork the second time: fork() failed");
    return 1;
  }
  if (pid > 0) {
    // Exit the first child process
    exit(0);
  }

  // Set file mode creation mask to a restrictive value
  umask(027);

  // Change the working directory, if specified
  if (in_working_directory) {
    if (chdir(in_working_directory) != 0) {
      last_error_set("Can't change working directory to %s",
                     in_working_directory);
      return 1;
    }
  }

  // Close all open file descriptors
  closefrom(0);

  // Redirect stdin, stdout, and stderr to /dev/null
  int fd = open("/dev/null", O_RDWR);
  if (fd != -1) {
    dup2(fd, STDIN_FILENO);
    dup2(fd, STDOUT_FILENO);
    dup2(fd, STDERR_FILENO);
    if (fd > STDERR_FILENO) {
      close(
          fd); // Close the file descriptor if it's not stdin, stdout, or stderr
    }
  } else {
    last_error_set("Can't open /dev/null");
    return 1;
  }

  // Install signal handlers
  signal(SIGCHLD, SIG_IGN); // Ignore child process termination
  signal(SIGTSTP, SIG_IGN); // Ignore terminal stop signals
  signal(SIGTTOU, SIG_IGN); // Ignore background writes to terminal
  signal(SIGTTIN, SIG_IGN); // Ignore background reads from terminal
  signal(SIGHUP, optional_signal_handler);
  signal(SIGTERM, optional_signal_handler);

  return 0;
}
