# Exemples de configuration

Cette page présente des exemples concrets de configuration pour différents scénarios d'utilisation.

## Exemple 1 : Automate F200 en TCP/IP avec HL7

### Contexte
- Automate : F200 (analyseur de chimie clinique)
- Connexion : TCP/IP sur réseau local
- Protocole : HL7 v2.6
- Environnement : Production

### Configuration complète

```ini
# Configuration F200 - Production
# Fichier : /etc/synlab/f200_prod.config

[global]
log-level = "INFO"
daemonize = true
log-file = "/var/log/synlab/f200.log"

[tcp]
bind-ip = "*************"    # IP dédiée à l'automate
bind-port = 1234
recv-timeout = 300           # 5 minutes pour les gros échantillons

[hl7]
version = "2.6"
sending-application = "F200"
sending-facility = "SYNLAB_MAIN"

[storing]
output-directory = "/var/data/synlab/f200"
create-subdirs = true
```

### Lancement

```bash
# Démarrage manuel
sudo -u synlab /opt/synlab/bin/f200 -c /etc/synlab/f200_prod.config

# Ou avec systemd
sudo systemctl start synlab-f200
```

### Message HL7 typique reçu

```
MSH|^~\&|F200|SYNLAB_MAIN|LIS|HOSPITAL|20231201143022||ORU^R01|F200_001234|P|2.6
PID|1||PAT123456^^^SYNLAB||MARTIN^JEAN||19750315|M|||123 RUE EXEMPLE^^PARIS^^75001^FR
OBR|1||CHEM_20231201_001|PANEL^Chimie Clinique|||20231201143000|||||||20231201143022
OBX|1|NM|GLU^Glucose||5.8|mmol/L|3.9-6.1|N|||F
OBX|2|NM|CHOL^Cholestérol total||4.2|mmol/L|<5.2|N|||F
OBX|3|NM|TG^Triglycérides||1.8|mmol/L|<1.7|H|||F
```

## Exemple 2 : Glucomètre portable en RS232 avec POCT

### Contexte
- Appareil : Glucomètre portable
- Connexion : RS232 via adaptateur USB
- Protocole : POCT simple
- Environnement : Cabinet médical

### Configuration

```ini
# Configuration Glucomètre - Cabinet
# Fichier : glucometre.config

[global]
log-level = "DEBUG"          # Debug pour le dépannage initial
daemonize = false           # Mode interactif pour les tests
log-file = "./logs/glucometre.log"

[rs232]
port-name = "/dev/ttyUSB0"
baud-rate = 9600
bits = 8
parity = "NONE"
stop-bits = 1
flow-control = "NONE"

[poct]
device-id = "GLUCO_001"

[storing]
output-directory = "./data/glucometre"
create-subdirs = true
```

### Test de connexion

```bash
# Vérifier le port USB
lsusb | grep -i serial

# Vérifier les permissions
ls -la /dev/ttyUSB0

# Lancer le driver
./bin/glucometre_driver -c glucometre.config
```

## Exemple 3 : Configuration multi-automates

### Contexte
- Plusieurs automates sur le même serveur
- Ports TCP différents
- Logs séparés

### Automate 1 : Hématologie

```ini
# hemato.config
[global]
log-level = "INFO"
daemonize = true
log-file = "/var/log/synlab/hemato.log"

[tcp]
bind-ip = "0.0.0.0"
bind-port = 1234

[hl7]
version = "2.5"
sending-application = "HEMATO"
sending-facility = "SYNLAB"

[storing]
output-directory = "/var/data/synlab/hemato"
```

### Automate 2 : Microbiologie

```ini
# micro.config
[global]
log-level = "INFO"
daemonize = true
log-file = "/var/log/synlab/micro.log"

[tcp]
bind-ip = "0.0.0.0"
bind-port = 1235              # Port différent

[hl7]
version = "2.6"
sending-application = "MICRO"
sending-facility = "SYNLAB"

[storing]
output-directory = "/var/data/synlab/micro"
```

### Script de démarrage

```bash
#!/bin/bash
# start_all_drivers.sh

DRIVERS_DIR="/opt/synlab/bin"
CONFIG_DIR="/etc/synlab"

# Démarrer l'hématologie
$DRIVERS_DIR/hemato -c $CONFIG_DIR/hemato.config &
echo "Hématologie démarrée (PID: $!)"

# Démarrer la microbiologie  
$DRIVERS_DIR/micro -c $CONFIG_DIR/micro.config &
echo "Microbiologie démarrée (PID: $!)"

# Attendre un peu et vérifier
sleep 5
netstat -tlnp | grep -E "(1234|1235)"
```

## Exemple 4 : Configuration de test/développement

### Contexte
- Environnement de développement
- Tests avec simulateur
- Logs détaillés

### Configuration

```ini
# test_dev.config
[global]
log-level = "DEBUG"
daemonize = false
log-file = "./test.log"
# stop-after = 10           # Arrêt après 10 messages pour les tests

[tcp]
bind-ip = "127.0.0.1"       # Local uniquement
bind-port = 9999            # Port de test

[hl7]
version = "2.6"
sending-application = "TEST"
sending-facility = "DEV"

[storing]
output-directory = "./test_data"
create-subdirs = false      # Pas de sous-répertoires pour les tests
```

### Script de test

```bash
#!/bin/bash
# test_driver.sh

# Nettoyer les anciens tests
rm -rf test_data/
mkdir test_data

# Démarrer le driver en arrière-plan
./bin/test_driver -c test_dev.config &
DRIVER_PID=$!

# Attendre le démarrage
sleep 2

# Envoyer des messages de test
echo "Envoi de messages de test..."
for i in {1..5}; do
    echo "MSH|^~\&|TEST|DEV|LIS|HOSPITAL|$(date +%Y%m%d%H%M%S)||ORU^R01|TEST_$i|P|2.6" | nc 127.0.0.1 9999
    echo "OBX|1|NM|GLU^Glucose||$((RANDOM % 10 + 4))|mmol/L|3.9-6.1|N|||F" | nc 127.0.0.1 9999
    echo "" | nc 127.0.0.1 9999  # Fin de message
    sleep 1
done

# Attendre le traitement
sleep 3

# Arrêter le driver
kill $DRIVER_PID

# Vérifier les résultats
echo "Messages reçus :"
ls -la test_data/
echo "Logs :"
tail test.log
```

## Exemple 5 : Configuration haute disponibilité

### Contexte
- Environnement de production critique
- Redondance et surveillance
- Rotation des logs

### Configuration principale

```ini
# prod_ha.config
[global]
log-level = "WARN"          # Logs minimaux en production
daemonize = true
log-file = "/var/log/synlab/prod.log"

[tcp]
bind-ip = "0.0.0.0"         # Toutes les interfaces
bind-port = 1234
recv-timeout = 600          # Timeout long pour la stabilité

[hl7]
version = "2.6"
sending-application = "PROD"
sending-facility = "SYNLAB_HA"

[storing]
output-directory = "/data/synlab/prod"  # Stockage rapide
create-subdirs = true
```

### Service systemd avec redémarrage

```ini
# /etc/systemd/system/synlab-prod.service
[Unit]
Description=Synlab Production Driver
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=synlab
Group=synlab
WorkingDirectory=/opt/synlab
ExecStart=/opt/synlab/bin/prod_driver -c /etc/synlab/prod_ha.config
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Sécurité
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/var/log/synlab /data/synlab

[Install]
WantedBy=multi-user.target
```

### Monitoring avec script

```bash
#!/bin/bash
# monitor_prod.sh

LOG_FILE="/var/log/synlab/prod.log"
ALERT_EMAIL="<EMAIL>"

# Vérifier que le processus tourne
if ! pgrep -f "prod_driver" > /dev/null; then
    echo "ALERTE: Driver de production arrêté!" | mail -s "Synlab Alert" $ALERT_EMAIL
    systemctl restart synlab-prod
fi

# Vérifier les erreurs récentes
ERROR_COUNT=$(tail -100 $LOG_FILE | grep -c ERROR)
if [ $ERROR_COUNT -gt 5 ]; then
    echo "ALERTE: $ERROR_COUNT erreurs détectées dans les logs" | mail -s "Synlab Alert" $ALERT_EMAIL
fi

# Vérifier l'espace disque
DISK_USAGE=$(df /data/synlab | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "ALERTE: Espace disque faible: ${DISK_USAGE}%" | mail -s "Synlab Alert" $ALERT_EMAIL
fi
```

## Conseils par environnement

### Développement
- `log-level = "DEBUG"`
- `daemonize = false`
- Ports de test (>9000)
- `stop-after` pour les tests automatisés

### Test/Recette
- `log-level = "INFO"`
- Configuration proche de la production
- Données de test réalistes

### Production
- `log-level = "WARN"` ou `"ERROR"`
- `daemonize = true`
- Surveillance et alertes
- Sauvegarde des configurations
- Rotation des logs

## Prochaines étapes

- [**Dépannage**](troubleshooting.md) - Résoudre les problèmes
- [**Configuration avancée**](config-reference.md) - Référence complète
- [**FAQ**](faq.md) - Questions fréquentes
