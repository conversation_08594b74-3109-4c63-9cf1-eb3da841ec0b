#include "message.h"

#include "log.h"
#include <stdlib.h>
#include <string.h>

static void *segment_clone(const void *segment) {
  Segment *seg = (Segment *)segment;
  return hl7_segment_clone(seg);
}

static void segment_destruct(void *segment) { hl7_segment_destruct(segment); }

Message *hl7_message_create() {
  Message *message;
  LOG_CHECK_MALLOC(message, malloc(sizeof(Message)));

  message->segments = dl_create(segment_clone, segment_destruct);
  if (!message->segments) {
    free(message);
    return NULL;
  }

  message->separators.component_separator = COMPONENT_SEPARATOR_DEFAULT;
  message->separators.escape_character = ESCAPE_SEPARATOR_DEFAULT;
  message->separators.field_separator = FIELD_SEPARATOR_DEFAULT;
  message->separators.repetition_separator = REPETITION_SEPARATOR_DEFAULT;
  message->separators.subcomponent_separator = SUBCOMPONENT_SEPARATOR_DEFAULT;

  return message;
}

Message *hl7_message_from_string(const char *str) {
  if (!str) {
    last_error_set("can't create a message from NULL string");
    return NULL;
  }

  // Create a new message
  Message *message = hl7_message_create();
  if (!message)
    return NULL;

  // Tokenize the input string using the segment delimiter '\r' (carriage
  // return)
  const char *segment_delimiter = "\r";
  char *hl7_str = strdup(str); // Make a modifiable copy of the input string
  char *segment_str = strtok(hl7_str, segment_delimiter);

  // Parse each segment
  while (segment_str) {
    // Create a Segment from the segment string
    Segment *segment =
        hl7_segment_from_string(segment_str, &message->separators);
    if (!segment) {
      // Clean up and return NULL if any segment parsing fails
      hl7_message_destruct(message);
      free(hl7_str);
      return NULL;
    }

    // Add the segment to the message
    hl7_message_add_segment(message, segment);

    // Get the next segment
    segment_str = strtok(NULL, segment_delimiter);
  }

  // Clean up
  free(hl7_str);

  return message;
}

char *hl7_message_to_string(const Message *message) {
  if (!message || !message->segments) {
    last_error_set("can't create a string from NULL message");
    return NULL;
  }

  size_t segments_count = dl_length(message->segments);

  if (segments_count == 0) {
    last_error_set(
        "can't create string representation of a message with 0 segments");
    return NULL;
  }

  // Array to hold the string representation of each segment
  char **substrs;
  LOG_CHECK_MALLOC(substrs, malloc(segments_count * sizeof(char *));)

  size_t total_length = 0;

  // Convert each segment to a string and calculate the total message length
  for (size_t i = 0; i < segments_count; i++) {
    const Segment *segment = dl_get(message->segments, i);
    char *child_str = hl7_segment_to_string(segment, &message->separators);
    if (!child_str) {
      // Clean up if conversion fails
      for (size_t j = 0; j < i; j++)
        free(substrs[j]);
      free(substrs);
      return NULL;
    }
    total_length += strlen(child_str) + 1; // +1 for the segment delimiter '\r'
    substrs[i] = child_str;
  }

  total_length++; // null terminator

  // Allocate memory for the final message string
  char *message_str;
  LOG_CHECK_MALLOC(message_str, malloc(total_length));

  // Concatenate the segments into the final message string
  message_str[0] = '\0'; // Start with an empty string
  for (size_t i = 0; i < segments_count; i++) {
    char *child_str = substrs[i];
    strcat(message_str, child_str);
    strcat(message_str, "\r"); // Add the segment delimiter after each segment
    free(child_str);           // Free the segment string after adding it to the
                               // message
  }

  free(substrs); // Free the array holding segment strings

  return message_str;
}

int hl7_message_add_segment(Message *message, Segment *segment) {
  return dl_append_by_value(message->segments, segment);
}

const Segment *hl7_message_get_segment_by_index(const Message *message,
                                                size_t index) {
  return dl_get(message->segments, index);
}

const Segment *hl7_message_get_segment_by_type(const Message *message,
                                               const SegmentType type) {
  for (size_t i = 0; i < dl_length(message->segments); i++) {
    const Segment *segment = dl_get(message->segments, i);
    if (hl7_segment_get_type(segment) == type)
      return segment;
  }

  last_error_set("no segment of type %s in message",
                 hl7_segment_type2str(type));
  return NULL;
}

size_t hl7_message_get_segments_count(const Message *message) {
  return dl_length(message->segments);
}

void hl7_message_destruct(Message *message) {
  if (!message)
    return;

  if (message->segments) {
    dl_destruct(message->segments);
    free(message->segments);
  }

  free(message);
}