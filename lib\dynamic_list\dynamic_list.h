/**
 * @file dynamic_list.h
 * @brief Simple dynamic list.
 */

#pragma once

#include <stddef.h>

/**
 * @brief Dynamic list.
 */
typedef struct DynamicListStruct {
  /**
   * @brief Current capacity of the list.
   */
  size_t capacity;

  /**
   * @brief Number of elements in the list.
   */
  size_t length;

  /**
   * @brief List content.
   */
  void **elements;

  /**
   * @brief Clone an element.
   *
   * Used to add elements into the struct.
   */
  void *(*clone_element)(const void *element);

  /**
   * @brief Destruct an element, i.e. frees ressources.
   *
   * Used to destruct the struct.
   */
  void (*destruct_element)(void *element);
} DynamicList;

/**
 * @brief Create a dynamic list for a specified element type.
 *
 * @param clone Function to clone an element.
 * @param destruct Function to destruct an element. (freeing the resources
 * allocated for the element to be destroyed).
 *
 * @return
 * - Success : The address of the created list.
 * - Error : NULL
 */
DynamicList *dl_create(void *(*clone)(const void *element),
                       void (*destruct)(void *element));

/**
 * @brief Clone a dynamic list.
 *
 * @param list Address of the structure to clone.
 *
 * @return
 * - Success : pointer to the cloned dynamic list
 * - Error : NULL
 */
DynamicList *dl_clone(const DynamicList *list);

/**
 * @brief Append a copy of an element to a dynamic list.
 *
 * @param list
 * @param element
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int dl_append(DynamicList *list, const void *element);

/**
 * @brief Add a copy of an element to a dynamic list.
 *
 * The previous element is freed.
 * The index has to be index less than the length of the list.
 *
 * @param list
 * @param element
 * @param index index in the list
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int dl_set(DynamicList *list, const void *element, size_t index);

/**
 * @brief Add an element to a dynamic list.
 *
 * The previous element is freed.
 * The index has to be index less than the length of the list.
 * Unlike `dl_set`, this function doesn't copy the added element : it takes
 * ownership of the passed pointer.
 *
 * @param list
 * @param element
 * @param index index in the list
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int dl_set_by_value(DynamicList *list, void *element, size_t index);

/**
 * @brief Append an element to a dynamic list.
 *
 * Unlike `dl_append`, this function doesn't copy the added element : it takes
 * ownership of the passed pointer.
 *
 * @param liste Address of the structure used to represent the dynamic
 * list to which (the copy of) the element will be added.
 * @param nouvel_element Address of the element to add.
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int dl_append_by_value(DynamicList *list, void *element);

/**
 * @brief Return an element of the list.
 *
 * @warning Returned value is a read-only pointer.
 *
 * @param liste Address of the dynamic list.
 * @param position Position of the element within the list. The first element
 * has position 0.
 *
 * @return
 * - Success : the element
 * - Error : NULL
 */
const void *dl_get(const DynamicList *list, size_t index);

/**
 * @brief Return the number of elements in a dynamic list.
 *
 * @param liste Address of the dynamic list.
 *
 * @return The number of elements in the dynamic list.
 */
size_t dl_length(const DynamicList *list);

/**
 * @brief Destroy a dynamic list.
 *
 * @param liste Address of the structure used to represent the dynamic
 * list to destroy.
 */
void dl_destruct(DynamicList *list);
