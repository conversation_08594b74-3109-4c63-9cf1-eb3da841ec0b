/**
 * @file error_reporting.h
 * @brief Minimal error reporting module for HL7 message parsing.
 */

#pragma once

#include "common.h"
#include "message.h"
#include "specification.h"

#include "field_enum.h"
#include "segment_enum.h"
#include "table_enum.h"

#include <stdbool.h>
#include <stddef.h>

/// @brief Check if a value exists in a given table.
///
/// @note Some that some tables are user defined. When this is the case, this
/// function doesn't check validity (it always returns `HL7_VALID`).
///
/// @param table_type
/// @param field_value
///
/// @return
/// - Success : `HL7_VALID` is value exists in table, `HL7_INVALID` otherwise
/// - Error : `HL7_ERROR` for internal error, `HL7_UNHANDLED` if the table is
/// not handled yet
void hl7_table_lookup(TableType table_type, const Field *field,
                      HL7ParsingErrorsHelper *helper);

/// @brief Checks if a field is valid regarding a certain type.
///
/// @param field field data string
/// @param ft field type
///
/// @return
/// - Success : `HL7_VALID` is field is valid, `HL7_INVALID` otherwise
/// - Error : `HL7_ERROR` for internal error, `HL7_UNHANDLED` a type is not
/// handled yet
void hl7_field_is_valid(const Field *field, const FieldInSegmentSpec *spec,
                        HL7ParsingErrorsHelper *helper);

/// @brief Checks if a segment is valid according to hl7 specs.
///
/// The validity of the segment is defined by the presence of the required
/// fields, and the validity of thoses fields. These properties are defined
/// by the hl7 standard version.
///
/// Since there are a lot of segment types, this function is meant to be
/// incrementally implemented, depending on current needs.
///
/// @param segment
///
/// @return
/// - Success : `HL7_VALID` is field is valid, `HL7_INVALID` otherwise
/// - Error : `HL7_ERROR` for internal error, `HL7_UNHANDLED` a type is not
/// handled yet
void hl7_segment_is_valid(const Segment *segment,
                          HL7ParsingErrorsHelper *helper);

/// @brief Checks if a message is valid according to hl7 specs.
///
/// The validity of the message is defined by the validity of its segments. This
/// is defined by the HL7 standard version.
///
/// @param message
///
/// @return
/// - Success : a caller-owned string for error messages ("" if no errors
/// were detected)
/// - Error : NULL
char *hl7_message_is_valid(const Message *message);
