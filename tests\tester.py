#!/usr/bin/python3

import argparse
import os
import shutil
import subprocess
import sys
import time
from dataclasses import dataclass
from enum import Enum
from typing import Callable

################################
# Constants
################################

SIMULATOR = "./automaton-simulator.py"
BINARIES_DIR = "../build/bin"
SPECS_DIR = "./specs"

CONFIG_DIRS = "./sample_configs"
SAMPLE_CONFIG_GLOBAL = f"{CONFIG_DIRS}/global.config"
SAMPLE_CONFIG_HL7 = f"{CONFIG_DIRS}/hl7.config"
SAMPLE_CONFIG_POCT = f"{CONFIG_DIRS}/poct.config"
SAMPLE_CONFIG_STORING = f"{CONFIG_DIRS}/storing.config"
SAMPLE_CONFIG_TCP = f"{CONFIG_DIRS}/tcp.config"
SAMPLE_CONFIG_RS232 = f"{CONFIG_DIRS}/rs232.config"
SAMPLE_CONFIG_RS232_SIMULATOR = f"{CONFIG_DIRS}/rs232_simulator.config"

RS232_TTY_0 = "/tmp/ttyS0"
RS232_TTY_1 = "/tmp/ttyS1"

LOGS_FOLDER = "./logs"

VALGRIND_COMMAND = [
    "valgrind",
    "--leak-check=full",
    "--track-origins=yes",
    "--show-leak-kinds=all",
    "--error-exitcode=1",
]
VALGRIND_OUTPUT = f"{LOGS_FOLDER}/valgrind_output.log"


################################
# Testing logic
################################


@dataclass
class TestFlow:
    name: str
    driver_args: list[str]
    client_bin: str
    client_args: list[str]
    setup: Callable = lambda: None
    terminate: Callable = lambda _: None


@dataclass
class Driver:
    name: str
    binary: str
    specs_folder: str
    use_carriage_returns: bool


def log_path(
    driver: Driver, test: TestFlow, use_valgrind: bool, error: bool, program: str
):
    path = f"{LOGS_FOLDER}/{driver.name}/{test.name}/"
    if not os.path.exists(path):
        os.makedirs(path)
    if use_valgrind:
        path += "valgrind_"
    path += program + "_"
    path += "output" if not error else "error"
    path += ".txt"
    return path


class TestFlowRunner:
    def __init__(self):
        self.server_process = None
        self.client_process = None

    def run_driver(self, test: TestFlow, driver: Driver, use_valgrind: bool):
        """Start the server binary with or without Valgrind."""
        server_command = [driver.binary] + test.driver_args

        if use_valgrind:
            server_command = VALGRIND_COMMAND + server_command
            print(f"Starting server with Valgrind: {' '.join(server_command)}")
        else:
            print(f"Starting server: {' '.join(server_command)}")

        output_name = log_path(driver, test, use_valgrind, False, "driver")
        error_name = log_path(driver, test, use_valgrind, True, "driver")

        # Start server process, capturing stdout and stderr in files
        with open(
            output_name,
            "w",
        ) as server_out, open(error_name, "w") as server_err:
            self.server_process = subprocess.Popen(
                server_command, stdout=server_out, stderr=server_err
            )

    def run_client(self, test: TestFlow, driver: Driver):
        """Run the client to send a message."""
        client_command = (
            [test.client_bin]
            + test.client_args
            + (["--usecarriagereturns"] if driver.use_carriage_returns else [])
        )
        print(f"Starting client: {' '.join(client_command)}")

        output_name = log_path(driver, test, False, False, "client")
        error_name = log_path(driver, test, False, True, "client")

        # Start client process, capturing stdout and stderr in files
        with open(output_name, "w") as client_out, open(error_name, "w") as client_err:
            self.client_process = subprocess.Popen(
                client_command, stdout=client_out, stderr=client_err
            )

    def stop_process(self, process, name: str):
        """Stop the given process."""
        if process:
            print(f"Stopping {name} (PID {process.pid})...")
            process.terminate()
            try:
                process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                print(
                    f"{name} did not stop in time, forcefully killing (PID {process.pid})..."
                )
                process.kill()
            print(f"{name} stopped.")

    def run_test(self, test_flow: TestFlow, driver: Driver, use_valgrind: bool = False):
        """Run the full test flow: start the server, run the client, and stop the server."""
        print(
            f"Starting test for {driver.name} {'with Valgrind' if use_valgrind else ''}"
        )
        try:
            context = test_flow.setup()

            self.run_driver(test_flow, driver, use_valgrind)
            # Allow the server some time to start
            time.sleep(1)

            self.run_client(test_flow, driver)

            assert self.client_process != None
            # Wait for the client to finish its task
            self.client_process.wait()

            # Allow the server some time to process
            time.sleep(2)

            test_flow.terminate(context)

        finally:
            self.stop_process(self.server_process, driver.name)


def clean():
    for path in [LOGS_FOLDER, "messages-bin", "messages-txt", "messages"]:
        if os.path.exists(path):
            shutil.rmtree(path)

    for file in os.listdir("./"):
        filename = os.fsdecode(file)
        if filename.endswith(".log"):
            os.remove(filename)


################################
# RS2323 setup
################################


def rs232_setup():
    # Launch socat to create virtual serial ports /tmp/ttyS1 and /tmp/ttyS2
    socat_process = subprocess.Popen(
        [
            "socat",
            "-d",
            "-d",
            f"pty,raw,echo=0,link={RS232_TTY_0}",
            f"pty,raw,echo=0,link={RS232_TTY_1}",
        ]
    )

    # Give socat time to set up the virtual ports
    time.sleep(1)

    # Check if the virtual ports are created
    if not (os.path.exists("/tmp/ttyS1") and os.path.exists("/tmp/ttyS2")):
        print("Failed to create virtual serial ports")
        socat_process.terminate()
        exit(1)

    print("Virtual serial ports created: /tmp/ttyS1 and /tmp/ttyS2")

    return socat_process


def rs232_terminate(socat_process):
    socat_process.terminate()
    socat_process.wait()


################################
# Tests specifications
################################


TCP_HL7_ARGS = [
    "-g",
    SAMPLE_CONFIG_GLOBAL,
    "-t",
    SAMPLE_CONFIG_TCP,
    "-a",
    SAMPLE_CONFIG_HL7,
    "-s",
    SAMPLE_CONFIG_STORING,
]

TCP_RS232_ARGS = [
    "-g",
    SAMPLE_CONFIG_GLOBAL,
    "-t",
    SAMPLE_CONFIG_RS232,
    "-a",
    SAMPLE_CONFIG_HL7,
    "-s",
    SAMPLE_CONFIG_STORING,
]

TCP_POCT_ARGS = [
    "-g",
    SAMPLE_CONFIG_GLOBAL,
    "-t",
    SAMPLE_CONFIG_TCP,
    "-a",
    SAMPLE_CONFIG_POCT,
    "-s",
    SAMPLE_CONFIG_STORING,
]

# F200 TCP

DriverF200 = Driver(
    name="F200",
    binary=f"{BINARIES_DIR}/f200",
    specs_folder=f"{SPECS_DIR}/f200-messages",
    use_carriage_returns=True,
)

TestF200CWEInfluenzaAB = TestFlow(
    "F200 CWE Influenza AB",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/CWEInfluenzaAB/message.txt",
        "RECEIVE",
    ],
)

TestF200QCQualitativeInfluenzaAB = TestFlow(
    "F200 QC Qualitative Influenza AB",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/QCQualitativeInfluenzaAB/message.txt",
        "RECEIVE",
    ],
)

TestF200QCQualitativeStrepA = TestFlow(
    "F200 QC Qualitative Strep A",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/QCQualitativeStrepA/message.txt",
        "RECEIVE",
    ],
)

TestF200QCQuantitativeuAlbumin = TestFlow(
    "F200 QC Quantitativeu Albumin",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/QCQuantitativeu-Albumin/message.txt",
        "RECEIVE",
    ],
)

# FIXME: why does this test specifically doesn't work ?
TestF200QualitativeStrepA = TestFlow(
    "F200 Qualitative Strep A",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/QualitativeStrepA/message.txt",
        "RECEIVE",
    ],
)


TestF200QuantitativeHbA1c = TestFlow(
    "F200 Quantitative HbA1c",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverF200.specs_folder}/QuantitativeHbA1c/message.txt",
        "RECEIVE",
    ],
)

# F200 RS232

DriverF200_RS232 = Driver(
    name="F200_RS232",
    binary=f"{BINARIES_DIR}/f200_rs232",
    specs_folder=f"{SPECS_DIR}/f200-messages",
    use_carriage_returns=True,
)

TestF200_RS232CWEInfluenzaAB = TestFlow(
    "F200_RS232 CWE Influenza AB",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/CWEInfluenzaAB/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)

TestF200_RS232QCQualitativeInfluenzaAB = TestFlow(
    "F200_RS232 QC Qualitative Influenza AB",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/QCQualitativeInfluenzaAB/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)

TestF200_RS232QCQualitativeStrepA = TestFlow(
    "F200_RS232 QC Qualitative Strep A",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/QCQualitativeStrepA/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)

TestF200_RS232QCQuantitativeuAlbumin = TestFlow(
    "F200_RS232 QC Quantitativeu Albumin",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/QCQuantitativeu-Albumin/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)

# FIXME: why does this test specifically doesn't work ?
TestF200_RS232QualitativeStrepA = TestFlow(
    "F200_RS232 Qualitative Strep A",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/QualitativeStrepA/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)


TestF200_RS232QuantitativeHbA1c = TestFlow(
    "F200_RS232 Quantitative HbA1c",
    TCP_RS232_ARGS,
    SIMULATOR,
    [
        "--rs232",
        "--config",
        SAMPLE_CONFIG_RS232_SIMULATOR,
        f"{DriverF200.specs_folder}/QuantitativeHbA1c/message.txt",
        "RECEIVE",
    ],
    rs232_setup,
    rs232_terminate,
)


# HB201DM

DriverHB201DM = Driver(
    name="Hemocue HB201DM",
    binary=f"{BINARIES_DIR}/hb201dm",
    specs_folder=f"{SPECS_DIR}/hb201dm-messages",
    use_carriage_returns=False,
)

TestHB201DMHello = TestFlow(
    "B201DM Hello",
    TCP_POCT_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverHB201DM.specs_folder}/hello.xml",
    ],
)

# TODO: more hb201dm tests

TestHB201DMComplete = TestFlow(
    "HB201DM Complete",
    TCP_POCT_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverHB201DM.specs_folder}/hello.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/status.xml",
        "RECEIVE",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
        f"{DriverHB201DM.specs_folder}/patient_observation1.xml",
        "RECEIVE",
    ],
)

# GEM5000

DriverGEM5000 = Driver(
    name="GEM5000",
    binary=f"{BINARIES_DIR}/gem5000",
    specs_folder=f"{SPECS_DIR}/gem5000-messages",
    use_carriage_returns=True,
)

TestGEM5000Upload = TestFlow(
    "GEM5000 upload",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/upload.txt",
        "RECEIVE",
    ],
)


TestGEM5000GemEvaluatorResults = TestFlow(
    "GEM5000 Gem Evaluator Results",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/gem-evaluator-results.txt",
        "RECEIVE",
    ],
)

TestGEM5000IamCarReports = TestFlow(
    "GEM5000 IQM CAR Reports",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/iqm-car-reports.txt",
        "RECEIVE",
    ],
)

TestGEM5000IqmCvpResults = TestFlow(
    "GEM5000 IQM CVP Results",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/iqm-cvp-results.txt",
        "RECEIVE",
    ],
)

TestGEM5000IQMDeltaCharData = TestFlow(
    "GEM5000 IQM Delta Char Data",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/iqm-delta-char-data.txt",
        "RECEIVE",
    ],
)

TestGEM5000RilibaekResults = TestFlow(
    "GEM5000 Rilibaek Results",
    TCP_HL7_ARGS,
    SIMULATOR,
    [
        "--config",
        SAMPLE_CONFIG_TCP,
        f"{DriverGEM5000.specs_folder}/rilibaek-results.txt",
        "RECEIVE",
    ],
)

# TODO: test aqt in real conditions to implement specifications
# The AQT documentation doesn't give any HL7 message example,
# nor does it lists which segments are used by the automaton.
# Thus, to specify the required messages,
# one has to test the driver with real AQT automatons.


class TestFlows(Enum):
    F200CWEInfluenzaAB = TestF200CWEInfluenzaAB
    F200QCQualitativeInfluenzaAB = TestF200QCQualitativeInfluenzaAB
    F200QCQualitativeStrepA = TestF200QCQualitativeStrepA
    F200QCQuantitativeuAlbumin = TestF200QCQuantitativeuAlbumin
    F200QualitativeStrepA = TestF200QualitativeStrepA
    F200QuantitativeHbA1c = TestF200QuantitativeHbA1c

    F200_RS232CWEInfluenzaAB = TestF200_RS232CWEInfluenzaAB
    F200_RS232QCQualitativeInfluenzaAB = TestF200_RS232QCQualitativeInfluenzaAB
    F200_RS232QCQualitativeStrepA = TestF200_RS232QCQualitativeStrepA
    F200_RS232QCQuantitativeuAlbumin = TestF200_RS232QCQuantitativeuAlbumin
    F200_RS232QualitativeStrepA = TestF200_RS232QualitativeStrepA
    F200_RS232QuantitativeHbA1c = TestF200_RS232QuantitativeHbA1c

    HB201DMHello = TestHB201DMHello
    HB201DMComplete = TestHB201DMComplete

    GEM5000Upload = TestGEM5000Upload
    GEM5000GemEvaluatorResults = TestGEM5000GemEvaluatorResults
    GEM5000IamCarReports = TestGEM5000IamCarReports
    GEM5000IqmCvpResults = TestGEM5000IqmCvpResults
    GEM5000IQMDeltaCharData = TestGEM5000IQMDeltaCharData
    GEM5000RilibaekResults = TestGEM5000RilibaekResults

    @staticmethod
    def list_names():
        return [test.name for test in TestFlows]

    @staticmethod
    def from_string(test_name: str):
        return TestFlows[test_name]


class Drivers(Enum):
    F200 = DriverF200
    F200_RS232 = DriverF200_RS232
    HB201DM = DriverHB201DM
    GEM5000 = DriverGEM5000

    @staticmethod
    def list_names():
        return [driver.name for driver in Drivers]

    @staticmethod
    def from_string(driver_name: str):
        return Drivers[driver_name]


TEST_FLOWS = {
    Drivers.F200: [
        TestFlows.F200CWEInfluenzaAB,
        TestFlows.F200QCQualitativeInfluenzaAB,
        TestFlows.F200QCQualitativeStrepA,
        TestFlows.F200QCQuantitativeuAlbumin,
        TestFlows.F200QuantitativeHbA1c,
        TestFlows.F200QualitativeStrepA,
    ],
    Drivers.F200_RS232: [
        TestFlows.F200_RS232CWEInfluenzaAB,
        TestFlows.F200_RS232QCQualitativeInfluenzaAB,
        TestFlows.F200_RS232QCQualitativeStrepA,
        TestFlows.F200_RS232QCQuantitativeuAlbumin,
        TestFlows.F200_RS232QuantitativeHbA1c,
        TestFlows.F200_RS232QualitativeStrepA,
    ],
    Drivers.HB201DM: [TestFlows.HB201DMComplete],
    Drivers.GEM5000: [
        TestFlows.GEM5000IamCarReports,
        TestFlows.GEM5000GemEvaluatorResults,
        TestFlows.GEM5000IqmCvpResults,
        TestFlows.GEM5000IQMDeltaCharData,
        TestFlows.GEM5000RilibaekResults,
        TestFlows.GEM5000Upload,
    ],
}


################################
# Main
################################


def main():
    if not os.path.exists(LOGS_FOLDER):
        os.mkdir(LOGS_FOLDER)

    # Define command-line arguments
    parser = argparse.ArgumentParser(description="Run drivers testflows.")
    parser.add_argument(
        "--valgrind",
        action="store_true",
        help="Run the driver under Valgrind (memory leaks detection)",
    )

    parser.add_argument(
        "--clean",
        action="store_true",
        help="Clean temporary files created by this script",
    )

    parser.add_argument(
        "tests",
        choices=TestFlows.list_names() + ["all"],
        nargs="*",
        help="Specify which test(s) to run (space-separated list)",
    )

    try:
        args = parser.parse_args()
    except argparse.ArgumentError as e:
        print(f"Argument parsing error: {e}\n")
        parser.print_help()
        sys.exit(1)

    if args.clean:
        clean()
        return

    # Choose the driver
    driver_names = Drivers.list_names()
    print("Please choose a driver:")
    for idx, driver_name in enumerate(driver_names, start=1):
        print(f"{idx}. {driver_name}")

    try:
        driver_selection = int(input("Enter the number of the driver to use: ").strip())
        selected_driver = Drivers.from_string(driver_names[driver_selection - 1])
    except (ValueError, IndexError):
        print("Invalid driver selection. Exiting.")
        sys.exit(1)

    # List the test flows associated with the chosen driver
    available_tests = [test for test in TEST_FLOWS[selected_driver]]
    print("\nAvailable test flows for the selected driver:")

    for idx, test in enumerate(available_tests, start=1):
        print(f"{idx}. {test.value.name}")
    print(f"{len(available_tests) + 1}. Run all test flows")

    try:
        # Prompt the user to select test flows
        selection = input(
            "Enter the number(s) of the test(s) to run (comma-separated): "
        ).strip()
        if selection == str(len(available_tests) + 1):
            selected_tests = available_tests
        else:
            selected_indices = [int(i) for i in selection.split(",")]
            selected_tests = [available_tests[i - 1] for i in selected_indices]
    except (ValueError, IndexError):
        print("Invalid selection. Exiting.")
        sys.exit(1)

    # Instantiate TestFlowRunner and run the test
    runner = TestFlowRunner()

    # Iterate through the selected tests and run each one
    for test in selected_tests:
        print(f"\nRunning test: {test}")
        runner.run_test(test.value, selected_driver.value, args.valgrind)


if __name__ == "__main__":
    main()
