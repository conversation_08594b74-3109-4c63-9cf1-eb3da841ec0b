#include "eqp.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec EQP_Spec[5] = {
    {"event type", FieldTypeCE, 250, REQUIRED, NOT_REPEATABLE, TableType0450},
    {"file name", FieldTypeST, 20, OP<PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"start date/time", FieldTypeTS, 26, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
    {"end date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"transaction data", FieldTypeFT, 65536, REQUIRED, NOT_REPEATABLE, TABLE_NONE},
};
// clang-format on

#endif