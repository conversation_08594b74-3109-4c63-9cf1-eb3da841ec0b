# FAQ - Questions fréquentes

## Questions générales

### Qu'est-ce que les drivers d'automates Synlab ?

Les drivers Synlab sont des logiciels qui permettent de connecter des automates de laboratoire (analyseurs, instruments de diagnostic) à votre système d'information. Ils récupèrent les résultats des analyses et les stockent dans un format standardisé.

### Quels automates sont supportés ?

Les drivers supportent tout automate capable de communiquer via :
- **TCP/IP** avec protocoles HL7 ou POCT
- **RS232** avec protocoles HL7 ou POCT

L'architecture modulaire permet d'ajouter facilement de nouveaux protocoles.

### Quelle est la différence entre HL7 et POCT ?

- **HL7** : Standard hospitalier complet, messages structurés, adapté aux gros volumes
- **POCT** : Format simplifié pour appareils de diagnostic au point de soin

## Installation et configuration

### Quels sont les prérequis système ?

**Système :** Linux RedHat (ou compatible)

**Bibliothèques requises :**
- libconfuse (configuration)
- libxml2 (parsing XML)
- libserialport (communication série)
- cmake, gcc (compilation)

### Comment compiler les drivers ?

```bash
mkdir build && cd build
cmake ..
make
```

Les binaires seront dans `build/bin/`.

### Puis-je utiliser plusieurs drivers simultanément ?

Oui ! Chaque driver utilise :
- Un port TCP différent
- Un fichier de configuration séparé
- Des logs distincts

Exemple :
```bash
./bin/hemato -c hemato.config &    # Port 1234
./bin/micro -c micro.config &      # Port 1235
```

### Comment migrer de l'ancien format de configuration ?

L'ancien système utilisait 4 fichiers séparés. Le nouveau utilise un seul fichier avec sections :

**Ancien :**
```bash
./driver -g global.config -t tcp.config -a hl7.config -s storing.config
```

**Nouveau :**
```bash
./driver -c unified.config
```

Copiez vos paramètres dans les sections `[global]`, `[tcp]`, `[hl7]`, `[storing]`.

## Utilisation

### Comment démarrer un driver ?

```bash
# Mode interactif (logs visibles)
./bin/f200 -c ma_config.config

# Mode démon (arrière-plan)
# Configurez daemonize = true dans [global]
./bin/f200 -c ma_config.config
```

### Comment arrêter un driver ?

```bash
# Mode interactif : Ctrl+C

# Mode démon :
kill $(pgrep f200)
# ou
systemctl stop synlab-f200
```

### Où sont stockées les données ?

Par défaut dans le répertoire configuré dans `[storing]` :

```ini
[storing]
output-directory = "./data"
create-subdirs = true
```

Structure créée :
```
data/
├── 2023/
│   ├── 12/
│   │   ├── 01/
│   │   │   ├── message_001.hl7
```

### Comment surveiller qu'un driver fonctionne ?

```bash
# Vérifier le processus
ps aux | grep mon_driver

# Vérifier l'écoute réseau
netstat -tln | grep 1234

# Vérifier les logs
tail -f /var/log/synlab/driver.log
```

## Problèmes courants

### "Address already in use" au démarrage

Un autre processus utilise le même port TCP.

**Solutions :**
```bash
# Trouver qui utilise le port
sudo netstat -tlnp | grep 1234

# Changer le port dans la configuration
bind-port = 1235

# Ou arrêter l'autre processus
sudo kill [PID]
```

### "Permission denied" sur le port série

L'utilisateur n'a pas les droits d'accès au port série.

**Solution :**
```bash
sudo usermod -a -G dialout $USER
newgrp dialout
```

### Le driver démarre mais ne reçoit aucun message

**Vérifications :**
1. L'automate est-il configuré avec la bonne IP/port ?
2. Le firewall bloque-t-il la connexion ?
3. Le câble réseau/série est-il connecté ?

**Test :**
```bash
# Test de connexion TCP
telnet [IP_DRIVER] [PORT]

# Test d'envoi de message
echo "TEST" | nc [IP_DRIVER] [PORT]
```

### Les messages sont reçus mais mal formatés

**Causes possibles :**
- Mauvais encodage (UTF-8 vs ASCII)
- Caractères de fin de ligne incorrects
- Structure HL7 invalide

**Diagnostic :**
```bash
# Examiner les caractères spéciaux
cat -A message_recu.txt

# Vérifier l'encodage
file message_recu.txt
```

### Le driver consomme beaucoup de CPU/mémoire

**Solutions :**
```bash
# Réduire le niveau de log
log-level = "ERROR"

# Vérifier l'espace disque
df -h /var/data/synlab

# Redémarrer périodiquement (workaround)
# Crontab : 0 2 * * * systemctl restart synlab-driver
```

## Configuration avancée

### Comment configurer plusieurs automates ?

Créez une configuration par automate avec des ports différents :

```ini
# automate1.config
[tcp]
bind-port = 1234

# automate2.config  
[tcp]
bind-port = 1235
```

### Comment activer les logs de debug ?

```ini
[global]
log-level = "DEBUG"
```

⚠️ **Attention :** Les logs DEBUG sont très verbeux.

### Comment configurer la rotation des logs ?

Utilisez logrotate :

```bash
# /etc/logrotate.d/synlab
/var/log/synlab/*.log {
    daily
    rotate 30
    compress
    missingok
    notifempty
}
```

### Comment démarrer automatiquement au boot ?

Créez un service systemd :

```ini
# /etc/systemd/system/synlab-f200.service
[Unit]
Description=Synlab F200 Driver
After=network.target

[Service]
Type=simple
User=synlab
ExecStart=/opt/synlab/bin/f200 -c /etc/synlab/f200.config
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl enable synlab-f200
sudo systemctl start synlab-f200
```

## Sécurité

### Comment sécuriser les communications ?

**Réseau :**
- Utilisez un VLAN dédié pour les automates
- Configurez un firewall restrictif
- Limitez l'accès aux ports nécessaires

**Système :**
- Créez un utilisateur dédié `synlab`
- Limitez les permissions des fichiers
- Chiffrez les données sensibles

### Comment sauvegarder les configurations ?

```bash
# Sauvegarder les configs
tar -czf synlab-backup-$(date +%Y%m%d).tar.gz /etc/synlab/

# Sauvegarder les données
rsync -av /var/data/synlab/ /backup/synlab/
```

## Performance

### Comment optimiser les performances ?

**Stockage :**
- Utilisez un SSD pour `/var/data/synlab`
- Montez avec options `noatime,nodiratime`
- Évitez les stockages réseau lents

**Configuration :**
```ini
[global]
log-level = "WARN"  # Moins de logs

[storing]
create-subdirs = false  # Si pas nécessaire
```

**Système :**
- Augmentez les limites de fichiers ouverts
- Optimisez les paramètres réseau TCP

### Combien de messages par seconde peut traiter un driver ?

Cela dépend de :
- Taille des messages
- Vitesse du stockage
- Puissance du serveur
- Niveau de log

**Ordre de grandeur :** 10-100 messages/seconde pour des messages HL7 typiques.

## Support et développement

### Comment signaler un bug ?

Fournissez :
1. Version du driver (`./driver --version`)
2. Configuration utilisée (masquer les données sensibles)
3. Logs d'erreur complets
4. Étapes pour reproduire le problème

### Comment demander une nouvelle fonctionnalité ?

Décrivez :
1. Le besoin métier
2. L'automate concerné
3. Le protocole utilisé
4. Des exemples de messages

### Comment contribuer au développement ?

Le projet suit les guidelines C documentées dans le README :
- Vérification des pointeurs null
- Pas d'émulation de méthodes dans les structs
- Usage de goto uniquement pour la libération mémoire

## Ressources

### Documentation technique

- **README.md** : Vue d'ensemble du projet
- **Implementation_guide.md** : Guide pour développeurs
- **MIGRATION_GUIDE.md** : Migration vers la configuration unifiée

### Outils externes

- **Doxygen** : Documentation API développeur
- **clang-format** : Formatage du code
- **cppcheck** : Analyse statique

### Liens utiles

- [HL7 Standard](http://www.hl7.org/)
- [libconfuse Documentation](https://github.com/libconfuse/libconfuse)
- [libserialport Documentation](https://sigrok.org/wiki/Libserialport)
