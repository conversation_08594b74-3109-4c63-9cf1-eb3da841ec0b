/**
 * @file daemon.h
 * @brief Daemon facility.
 */

#pragma once

/**
 * @brief Turn the current process into a daemon.
 *
 * @param in_working_directory Path to the working directory.
 * If you set this value to NULL, then no working directory will be set.
 * @param optional_signal_handler Pointer to a signal handler. The function
 * signature must be: void default_signal_handler(int sig) If the value of this
 * parameter is NULL, then the default signal handler is used.
 *
 * @return
 * - Success : 0
 * - Error : 1
 */
int daemonize(const char *in_working_directory,
              void (*optional_signal_handler)(int));
