/**
 * @file unified_config_file.h
 * @brief Unified configuration file parser for all application layers.
 *
 * This module replaces the individual config file parsers and handles
 * a single configuration file with sections for different layers:
 * [global], [tcp], [rs232], [hl7], [poct], [storing]
 */

#pragma once

#include "global_config.h"

// Forward declarations for configuration structures
#ifdef TCP
#include "tcp_config.h"
#elif RS232
#include "rs232_config.h"
#endif

#ifdef HL7
#include "hl7_config.h"
#elif POCT
#include "poct_config.h"
#endif

#ifdef FILE_STORING
#include "file_storing_config.h"
#endif

/**
 * @brief Parse unified configuration file and populate all configuration structures.
 * 
 * This function replaces the individual config file parsers:
 * - global_config_file_parse()
 * - transport_config_file_parse()
 * - application_config_file_parse()
 * - storing_config_file_parse()
 * 
 * @param global_config Pointer to global configuration structure
 * @param transport_config Pointer to transport configuration structure
 * @param application_config Pointer to application configuration structure
 * @param storing_config Pointer to storing configuration structure
 * @param config_file_path Path to the unified configuration file
 * @return 0 on success, non-zero on error
 */
int unified_config_file_parse(GlobalConfiguration *global_config,
                             TransportConfiguration *transport_config,
                             ApplicationConfiguration *application_config,
                             StoringConfiguration *storing_config,
                             const char *config_file_path);

/**
 * @brief Validate that all required configuration sections are present.
 * 
 * @param config_file_path Path to the configuration file
 * @return 0 if valid, non-zero if missing required sections
 */
int unified_config_file_validate(const char *config_file_path);
