# Guide de Migration - Configuration Unifiée

## Vue d'ensemble

Ce projet a été refactorisé pour utiliser un seul fichier de configuration unifié au lieu de 4 fichiers séparés. Cette migration simplifie l'utilisation et la maintenance des configurations.

## Changements Principaux

### Avant (Ancien système)
```bash
./f200 -g f200conf/global.config -t f200conf/tcp.config -a f200conf/hl7.config -s f200conf/storing.config
```

### Après (Nouveau système)
```bash
./f200 -c f200conf/unified.config
```

## Structure du Fichier de Configuration Unifié

Le nouveau fichier de configuration utilise des sections pour organiser les paramètres :

```
global {
  log-level = "DEBUG"
  daemonize = false
  log-file = "./main.log"
}

tcp {
  bind-ip = "127.0.0.1"
  bind-port = 1234
  recv-timeout = 120
}

rs232 {
  port-name = "/tmp/ttyS1"
  baud-rate = 9600
  bits = 8
  parity = "NONE"
  stop-bits = 1
  flow-control = "NONE"
}

hl7 {
  sending-application = "test-app-1234"
  sending-facility = "test-facility-1234"
  delay-before-ack = 1
}

poct {
  response-timeout-ms = 2000
}

storing {
  storing-dir = "."
}
```

## Migration de vos Configurations Existantes

### Étape 1 : Créer le fichier unifié
Créez un nouveau fichier (ex: `myconfig.config`) avec toutes les sections nécessaires.

### Étape 2 : Copier les paramètres
Copiez les paramètres de vos anciens fichiers dans les sections appropriées :

- `global.config` → section `[global]`
- `tcp.config` → section `[tcp]`
- `rs232.config` → section `[rs232]`
- `hl7.config` → section `[hl7]`
- `poct.config` → section `[poct]`
- `storing.config` → section `[storing]`

### Étape 3 : Mettre à jour vos scripts
Remplacez les anciens appels avec 4 paramètres par le nouveau avec un seul paramètre `-c`.

## Exemples de Configurations

### Configuration TCP + HL7
```
global {
  log-level = "INFO"
  daemonize = false
  log-file = "./tcp_hl7.log"
}

tcp {
  bind-ip = "0.0.0.0"
  bind-port = 8080
  recv-timeout = 300
}

hl7 {
  sending-application = "hospital-system"
  sending-facility = "lab-department"
  delay-before-ack = 2
}

storing {
  storing-dir = "./messages"
}
```

### Configuration RS232 + POCT
```
global {
  log-level = "DEBUG"
  daemonize = true
  log-file = "/var/log/rs232_poct.log"
}

rs232 {
  port-name = "/dev/ttyUSB0"
  baud-rate = 115200
  bits = 8
  parity = "EVEN"
  stop-bits = 2
  flow-control = "RTSCTS"
}

poct {
  response-timeout-ms = 5000
}

storing {
  storing-dir = "/var/data/poct"
}
```

## Fichiers Modifiés

### Nouveaux fichiers
- `include/unified_config_file.h` - Header du nouveau parser
- `src/unified_config_file.c` - Implémentation du parser unifié
- `tests/sample_configs/unified.config` - Exemple de configuration unifiée

### Fichiers modifiés
- `include/cli.h` - Interface CLI simplifiée
- `src/cli.c` - Implémentation CLI avec paramètre unique
- `src/main.c` - Utilisation du nouveau parser
- `src/CMakeLists.txt` - Build system mis à jour
- `README.md` - Documentation mise à jour

## Avantages de la Nouvelle Approche

1. **Simplicité** : Un seul fichier au lieu de 4
2. **Maintenance** : Plus facile de gérer une configuration centralisée
3. **Lisibilité** : Structure claire avec sections nommées
4. **Flexibilité** : Possibilité d'ajouter facilement de nouvelles sections
5. **Moins d'erreurs** : Moins de risques d'oublier un fichier de configuration

## Compatibilité

- ✅ Toutes les fonctionnalités existantes sont préservées
- ✅ Tous les paramètres de configuration sont supportés
- ✅ La validation des paramètres est maintenue
- ✅ Les messages d'erreur sont améliorés

## Tests

Des fichiers de test ont été créés pour valider la migration :
- `tests/sample_configs/tcp_hl7.config`
- `tests/sample_configs/rs232_poct.config`
- `tests/test_unified_config.c`

## Support

En cas de problème avec la migration, vérifiez :
1. Que toutes les sections requises sont présentes dans votre fichier unifié
2. Que la syntaxe des paramètres est correcte
3. Que les chemins de fichiers sont valides
4. Que les permissions sont correctes

Les anciens fichiers de configuration peuvent être conservés comme référence pendant la période de transition.
