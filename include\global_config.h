/**
 * @file global_config.h
 * @brief Global configuration specification for the application.
 *
 * The parameters specified here are transport-agnostic.
 */

#pragma once

#define CONFIG_LOG_LEVEL "log-level"
#define CONFIG_LOG_LEVEL_HELP                                                  \
  "Logging level. Possible values : TRACE, DEBUG, INFO, ERROR, FATA<PERSON>"
#define CONFIG_DAEMONIZE "daemonize"
#define CONFIG_DAEMONIZE_HELP                                                  \
  "Whether the process should run in background or not. Possible values : "    \
  "true, false"
#define CONFIG_DAEMONIZE_INIT -1
#define CONFIG_STOP_AFTER "stop-after"
#define CONFIG_STOP_AFTER_HELP                                                 \
  "Period in seconds which after the process will stop. -1 means runnning "    \
  "forever.(Only used for debugging purposes)"
#define CONFIG_STOP_AFTER_INIT -2
#define CONFIG_LOG_FILE "log-file"
#define CONFIG_LOG_FILE_HELP "Logging file path. Logs will be written there."

#define DEFAULT_LOG_DIR "/var/log"

/**
 *  * @brief Overall configuration specification for the application.
 */
typedef struct {
  /**
   * @brief Log level. Can be either of TRACE, DEBUG, INFO, WARNING, ERROR.
   * */
  char *log_level;
  /**
   *  @brief Whether the process should be daemonized (run in background) or
   * not.
   */
  int daemonize;
  /**
   * @brief Running time, which after the application will stop.
   * Used for testing purposes only.
   */
  long int stop_after;
  /**
   * @brief Logging file path. Logs will be written there.
   */
  char *log_file;
} GlobalConfiguration;

/**
 * @brief Initialise la structure de données utilisée pour le stockage des
 * paramètres de configuration.
 *
 * @param configuration Adresse de la structure de données utilisée pour le
 * stockage des paramètres de configuration.
 */
void global_configuration_init(GlobalConfiguration *config);

/**
 * @brief Libère les ressources allouées pour le stockage des valeurs de
 * configuration.
 *
 * @param configuration Adresse de la structure de données utilisée pour le
 * stockage des paramètres de configuration.
 */
void global_configuration_destruct(GlobalConfiguration *config);

char *global_configuration_to_string(const GlobalConfiguration *config);
