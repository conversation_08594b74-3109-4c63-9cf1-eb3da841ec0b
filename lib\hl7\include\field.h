/**
 * @file field.h
 */

#pragma once

#include "common.h"
#include <stddef.h>
#include <sys/types.h>

typedef enum {
  FIELD_REPEATED,
  FIELD_WITH_COMPONENTS,
  FIELD_WITH_SUBCOMPONENTS,
  FIELD_SIMPLE,
} FieldTreeType;

typedef struct FieldStruct {
  FieldTreeType type;
  char *value;                   // for simple fields
  struct FieldStruct **children; // for composed fields
  size_t children_count;
  ssize_t length; // in chars
} Field;

/**
 * @brief Creates a field.
 *
 * @return
 * - Success: pointer to the created field
 * - Error: NULL
 */
Field *hl7_field_create();

/**
 * @brief Creates an field ("").
 *
 * @return
 * - Success: pointer to the created field
 * - Error: NULL
 */
Field *hl7_field_create_empty();

/**
 * @brief Clones a field.
 *
 * @param source field to copy
 *
 * @return
 * - Success: pointer to the created field
 * - Error: NULL
 */
Field *hl7_field_clone(const Field *source);

/**
 * @brief Parses a string, creating a new field.
 *
 * @return
 * - Success: pointer to the created field
 * - Error: NULL
 */
Field *hl7_field_from_string(const char *str, const Separators *separators);

/**
 * @brief Gets the total length, in chars, of a field.
 *
 * @param field
 *
 * @return the length in chars
 */
size_t hl7_field_length(const Field *field);

/**
 * @brief Generates a readable string representing a field.
 *
 * @param field
 * @param separators
 *
 * @return string representation of the field
 */
char *hl7_field_to_string(const Field *field, const Separators *separators);

/**
 * @brief Adds children to a field.
 *
 * @param field
 * @param subfields_children children to add. These children are now owned by
 * the father.
 * @param type
 *
 * @return
 * - Success: 0
 * - Error: 1
 */
int hl7_field_add_children(Field *field, Field **subfields_children,
                           FieldTreeType type);

/**
 * @brief Gets a child from a field.
 *
 * @param field
 * @param index index of the child
 *
 * @return
 * - Success: the child
 * - Error: NULL
 */
Field *hl7_field_get_child(const Field *field, size_t index);

/**
 * @brief Returns the number of children of the field.
 *
 * @param field
 *
 * @return The number of children in the field (0 if it is a simple field).
 */
size_t hl7_field_get_children_count(const Field *field);

/**
 * @brief Get tree type of a field
 *
 * @param field
 *
 * @return tree type of the field
 */
FieldTreeType hl7_field_get_type(const Field *field);

/**
 * @brief Set tree type of a field
 *
 * @param field
 * @param tree_type
 */
void hl7_field_set_type(Field *field, FieldTreeType tree_type);

/**
 * @brief Get the value for `FIELD_SIMPLE` field
 *
 * @param field
 *
 * @return value of the field
 */
const char *hl7_field_get_value(const Field *field);

/**
 * @brief Set the value for `FIELD_SIMPLE` field
 *
 * @param field
 * @param value
 */
void hl7_field_set_value(Field *field, const char *value);

/**
 * @brief Destroys a field, freeing its allocated ressources.
 *
 * @param field
 */
void hl7_field_destruct(Field *field);
