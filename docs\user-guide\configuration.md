# Configuration des Drivers

Les drivers utilisent un **fichier de configuration unifié** qui remplace l'ancien système à 4 fichiers séparés. Cette approche simplifie la gestion et réduit les erreurs.

## Structure du fichier de configuration

Le fichier utilise des **sections** pour organiser les paramètres :

```ini
# Commentaires commencent par #

[global]
# Configuration générale

[tcp]
# Configuration TCP/IP

[rs232] 
# Configuration RS232

[hl7]
# Configuration HL7

[poct]
# Configuration POCT

[storing]
# Configuration stockage
```

## Section [global] - Configuration générale

```ini
[global]
log-level = "DEBUG"          # Niveau de log : DEBUG, INFO, WARN, ERROR
daemonize = false            # Lancer en arrière-plan (true/false)
log-file = "./main.log"      # Fichier de log
# stop-after = -1            # Arrêt automatique (-1 = jamais, pour tests)
```

### Paramètres disponibles

| Paramètre | Type | Description | Valeurs |
|-----------|------|-------------|---------|
| `log-level` | String | Niveau de verbosité | DEBUG, INFO, WARN, ERROR |
| `daemonize` | Boolean | Mode démon | true, false |
| `log-file` | String | Chemin du fichier de log | Chemin relatif ou absolu |
| `stop-after` | Integer | Arrêt après N messages | -1 (jamais), >0 (nombre) |

## Section [tcp] - Transport TCP/IP

```ini
[tcp]
bind-ip = "127.0.0.1"        # Adresse IP d'écoute
bind-port = 1234             # Port d'écoute
recv-timeout = 120           # Timeout de réception (secondes)
```

### Paramètres TCP

| Paramètre | Type | Description | Exemple |
|-----------|------|-------------|---------|
| `bind-ip` | String | IP d'écoute | "0.0.0.0", "*************" |
| `bind-port` | Integer | Port d'écoute | 1234, 8080, 3000 |
| `recv-timeout` | Integer | Timeout réception | 30, 120, 300 |

!!! tip "Conseil réseau"
    - Utilisez `"0.0.0.0"` pour écouter sur toutes les interfaces
    - Utilisez `"127.0.0.1"` pour les tests locaux uniquement

## Section [rs232] - Transport série

```ini
[rs232]
port-name = "/dev/ttyS0"     # Port série (Linux)
baud-rate = 9600             # Vitesse de transmission
bits = 8                     # Bits de données
parity = "NONE"              # Parité
stop-bits = 1                # Bits d'arrêt
flow-control = "NONE"        # Contrôle de flux
```

### Paramètres RS232

| Paramètre | Type | Description | Valeurs possibles |
|-----------|------|-------------|-------------------|
| `port-name` | String | Port série | "/dev/ttyS0", "/dev/ttyUSB0", "COM1" |
| `baud-rate` | Integer | Vitesse | 9600, 19200, 38400, 115200 |
| `bits` | Integer | Bits de données | 7, 8 |
| `parity` | String | Contrôle parité | "NONE", "EVEN", "ODD" |
| `stop-bits` | Integer | Bits d'arrêt | 1, 2 |
| `flow-control` | String | Contrôle flux | "NONE", "HARDWARE", "SOFTWARE" |

!!! warning "Ports série sous Linux"
    Assurez-vous que l'utilisateur a les droits d'accès au port série :
    ```bash
    sudo usermod -a -G dialout $USER
    ```

## Section [hl7] - Protocole HL7

```ini
[hl7]
version = "2.6"              # Version HL7
sending-application = "LAB"   # Application émettrice
sending-facility = "SYNLAB"   # Établissement émetteur
```

## Section [poct] - Protocole POCT

```ini
[poct]
device-id = "POCT001"        # Identifiant de l'appareil
```

## Section [storing] - Stockage

```ini
[storing]
output-directory = "./data"   # Répertoire de sortie
create-subdirs = true        # Créer des sous-répertoires
```

## Exemples complets

### Configuration TCP/IP + HL7
```ini
[global]
log-level = "INFO"
log-file = "./logs/f200.log"
daemonize = false

[tcp]
bind-ip = "*************"
bind-port = 1234
recv-timeout = 300

[hl7]
version = "2.6"
sending-application = "F200"
sending-facility = "SYNLAB"

[storing]
output-directory = "./data/f200"
create-subdirs = true
```

### Configuration RS232 + POCT
```ini
[global]
log-level = "DEBUG"
log-file = "./logs/poct.log"

[rs232]
port-name = "/dev/ttyUSB0"
baud-rate = 19200
bits = 8
parity = "NONE"
stop-bits = 1
flow-control = "NONE"

[poct]
device-id = "POCT001"

[storing]
output-directory = "./data/poct"
```

## Validation de la configuration

Le driver valide automatiquement la configuration au démarrage. En cas d'erreur :

1. **Vérifiez la syntaxe** : sections entre crochets, paramètres = valeurs
2. **Vérifiez les types** : entiers sans guillemets, chaînes avec guillemets
3. **Vérifiez les chemins** : répertoires existants et accessibles
4. **Vérifiez les permissions** : droits d'écriture pour les logs et données

!!! example "Fichier de test"
    Un exemple complet est disponible dans `tests/sample_configs/unified.config`
