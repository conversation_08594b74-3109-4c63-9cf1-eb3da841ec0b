#include "rs232_layer.h"

#include "application_to_transport.h"
#include "log.h"
#include "transport_to_application.h"
#include <libserialport.h>
#include <stdlib.h>
#include <unistd.h>

#define RS232_BUFFER_CAPACITY 1024 * 1000
#define TIMEOUT_MS 500 // Timeout to avoid busy looping

static TransportConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static unsigned char buffer[RS232_BUFFER_CAPACITY];
static bool stop_flag;
struct sp_port *port;

static int check(enum sp_return result) {
  /* For this example we'll just exit on any error by calling abort(). */
  char *error_message;
  switch (result) {
  case SP_ERR_ARG:
    printf("Error: Invalid argument.\n");
    abort();
  case SP_ERR_FAIL:
    error_message = sp_last_error_message();
    printf("Error: Failed: %s\n", error_message);
    sp_free_error_message(error_message);
    abort();
  case SP_ERR_SUPP:
    printf("Error: Not supported.\n");
    abort();
  case SP_ERR_MEM:
    printf("Error: Couldn't allocate memory.\n");
    abort();
  case SP_OK:
  default:
    return result;
  }
}

int transport_layer_init(TransportConfiguration *init_config,
                         PubSub *init_transport2application,
                         PubSub *init_application2transport) {
  config = init_config;
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  stop_flag = false;

  check(sp_get_port_by_name(config->port_name, &port));
  check(sp_set_baudrate(port, config->baud_rate));
  check(sp_set_bits(port, config->bits));
  check(sp_set_parity(port, config->parity));
  check(sp_set_stopbits(port, config->stop_bits));
  check(sp_set_flowcontrol(port, config->flow_control));

  if (add_subscriber(application2transport, transport_layer_send, NULL))
    return 1;

  return 1;
}

int transport_layer_read_publish_loop() {
  while (stop_flag) {
    int bytes_read =
        check(sp_nonblocking_read(port, buffer, RS232_BUFFER_CAPACITY));

    // avoid busy waiting
    if (!bytes_read)
      usleep(TIMEOUT_MS);

    const TransportToApplicationData data = {
        .bytes = buffer,
        .length = bytes_read,
    };

    publish_message(transport2application, &data);
  }

  return 0;
}

void transport_layer_send(void *void_context, const void *void_data) {
  (void)void_context;
  const ApplicationToTransportData *data =
      (ApplicationToTransportData *)void_data;

  size_t result;
  result = check(sp_nonblocking_write(port, data->bytes, data->length));
  if (result == data->length)
    printf("Sent %zu bytes successfully.\n", data->length);
  else
    printf("%zu/%zu bytes sent.\n", result, data->length);
}

void transport_layer_end() {
  stop_flag = true;
  sp_free_port(port);
}
