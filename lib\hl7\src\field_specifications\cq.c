#include "cq.h"

#if HL7_VERSION == 240

const SubFieldSpec CQ_SPEC[2] = {
    {FieldTypeNM, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeST, 0, OPTION<PERSON>, TABLE_NONE},
};

#elif HL7_VERSION == 250

const SubFieldSpec CQ_SPEC[2] = {
    {FieldTypeNM, 16, OP<PERSON>ON<PERSON>, TABLE_NONE},
    {FieldTypeCE, 483, OP<PERSON>ON<PERSON>, TABLE_NONE},
};

#elif HL7_VERSION == 260

const SubFieldSpec CQ_SPEC[2] = {
    {FieldTypeNM, 16, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OP<PERSON>ON<PERSON>, TABLE_NONE},
};

#endif
