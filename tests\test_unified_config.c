/**
 * @file test_unified_config.c
 * @brief Test suite for unified configuration parser.
 */

#include "unified_config_file.h"
#include "global_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

// Include appropriate config headers based on build configuration
#ifdef TCP
#include "tcp_config.h"
#elif RS232
#include "rs232_config.h"
#endif

#ifdef HL7
#include "hl7_config.h"
#elif POCT
#include "poct_config.h"
#endif

#ifdef FILE_STORING
#include "file_storing_config.h"
#endif

void test_unified_config_parsing() {
    printf("Testing unified configuration parsing...\n");
    
    GlobalConfiguration global_config;
    TransportConfiguration transport_config;
    ApplicationConfiguration application_config;
    StoringConfiguration storing_config;
    
    // Initialize configurations
    global_configuration_init(&global_config);
    transport_configuration_init(&transport_config);
    application_configuration_init(&application_config);
    storing_configuration_init(&storing_config);
    
    // Test parsing the unified config file
    int result = unified_config_file_parse(&global_config, &transport_config,
                                          &application_config, &storing_config,
                                          "tests/sample_configs/unified.config");
    
    if (result != 0) {
        printf("ERROR: Failed to parse unified configuration file\n");
        exit(1);
    }
    
    // Verify global configuration
    assert(global_config.log_level != NULL);
    assert(strcmp(global_config.log_level, "DEBUG") == 0);
    assert(global_config.daemonize == 0); // false
    assert(global_config.log_file != NULL);
    assert(strcmp(global_config.log_file, "./main.log") == 0);
    
#ifdef TCP
    // Verify TCP configuration
    assert(transport_config.bind_ip != NULL);
    assert(strcmp(transport_config.bind_ip, "127.0.0.1") == 0);
    assert(transport_config.bind_port == 1234);
    assert(transport_config.recv_timeout == 120);
#endif

#ifdef RS232
    // Verify RS232 configuration
    assert(transport_config.port_name != NULL);
    assert(strcmp(transport_config.port_name, "/tmp/ttyS1") == 0);
    assert(transport_config.baud_rate == 9600);
    assert(transport_config.bits == 8);
    assert(transport_config.stop_bits == 1);
#endif

#ifdef HL7
    // Verify HL7 configuration
    assert(application_config.sending_application != NULL);
    assert(strcmp(application_config.sending_application, "test-app-1234") == 0);
    assert(application_config.sending_facility != NULL);
    assert(strcmp(application_config.sending_facility, "test-facility-1234") == 0);
    assert(application_config.delay_before_ack == 1);
#endif

#ifdef POCT
    // Verify POCT configuration
    assert(application_config.response_timeout_ms == 2000);
#endif

#ifdef FILE_STORING
    // Verify storing configuration
    assert(storing_config.storing_directory != NULL);
    assert(strcmp(storing_config.storing_directory, ".") == 0);
#endif
    
    printf("✓ All configuration values parsed correctly\n");
    
    // Clean up
    global_configuration_destruct(&global_config);
    transport_configuration_destruct(&transport_config);
    application_configuration_destruct(&application_config);
    storing_configuration_destruct(&storing_config);
}

void test_config_file_validation() {
    printf("Testing configuration file validation...\n");
    
    // Test with existing file
    int result = unified_config_file_validate("tests/sample_configs/unified.config");
    assert(result == 0);
    printf("✓ Valid configuration file validation passed\n");
    
    // Test with non-existing file
    result = unified_config_file_validate("non_existing_file.config");
    assert(result != 0);
    printf("✓ Invalid configuration file validation passed\n");
}

int main() {
    printf("=== Unified Configuration Test Suite ===\n\n");
    
    test_config_file_validation();
    test_unified_config_parsing();
    
    printf("\n=== All tests passed! ===\n");
    return 0;
}
