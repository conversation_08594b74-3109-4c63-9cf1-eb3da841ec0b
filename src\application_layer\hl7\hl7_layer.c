#include "hl7_layer.h"

#include "application_to_storing.h"
#include "application_to_transport.h"
#include "binary_buffer.h"
#include "hl7.h"
#include "hl7_config.h"
#include "log.h"
#include "transport_to_application.h"
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024 * 1000

// unlike input_output model, this module doesn't use
// dynamic allocationn
// this choice was taken considering embedded systems requirements
// static allocation allows compile-time memory allocation,
// helping to prevent running short of memory at runtime
// schema is : Receving message → Storage (binary + text) → Parsing → Validation → Sending ACK (MSH+MSA)

static unsigned char buffer[BUFFER_SIZE];
BinaryBuffer *bb;

static ApplicationConfiguration *config;
static PubSub *transport2application;
static PubSub *application2transport;
static PubSub *application2storing;

#if HL7_VERSION == 260 || HL7_VERSION == 250 || HL7_VERSION == 240

Message *generate_ack(const Message *message, const int success,
                      const char *sending_application,
                      const char *sending_facility) {
  const Segment *sender_msh_segment =
      hl7_message_get_segment_by_type(message, SegmentTypeMSH);
  const Separators *separators = &message->separators;

  // is there an msh in sending application message ?
  if (!sender_msh_segment) {
    last_error_set("can't generate ACK : no MSH segment in sender message");
    return NULL;
    
  }

  const Field *message_type =
      hl7_segment_get_field(sender_msh_segment, MSHMessageType);
  const Field *event_type = hl7_field_get_child(message_type, 1);

  if (!event_type) {
    last_error_display(LOG_ERROR, "can't get event type from sender message");
    return NULL;
  }

  char *event_type_str = hl7_field_to_string(event_type, separators);
  char *message_type_str;

  // Check if event_type_str is empty
  if (!event_type_str) {
    last_error_display(LOG_ERROR, "event_type_str is null");
    return NULL;
  }

  // Not necessary to check if event_type_str > SIZE_MAX -9 - not possible
  LOG_CHECK_MALLOC(message_type_str, malloc(strlen(event_type_str) + 9));
  message_type_str[0] = '\0';
  strcat(message_type_str, "ACK^");
  strcat(message_type_str, event_type_str);
  strcat(message_type_str, "^ACK");
  free(event_type_str);

  // msh segment
  Segment *ack_msh = hl7_segment_create();

  hl7_segment_set_type(ack_msh, SegmentTypeMSH);
  if (hl7_segment_copy_field(ack_msh, MSHFieldSeparator, sender_msh_segment,
                             MSHFieldSeparator) ||
      hl7_segment_copy_field(ack_msh, MSHEncodingCharacters, sender_msh_segment,
                             MSHEncodingCharacters) ||
      hl7_segment_set_field_string(ack_msh, sending_application, separators,
                                   MSHSendingApplication) ||
      hl7_segment_set_field_string(ack_msh, sending_facility, separators,
                                   MSHSendingFacility) ||
      hl7_segment_copy_field(ack_msh, MSHReceivingApplication,
                             sender_msh_segment, MSHSendingApplication) ||
      hl7_segment_copy_field(ack_msh, MSHReceivingFacility, sender_msh_segment,
                             MSHSendingFacility) ||
      hl7_segment_set_field_string(ack_msh, hl7_generate_date_time(),
                                   separators, MSHDateTimeOfMessage) ||
      hl7_segment_set_field_string(ack_msh, message_type_str, separators,
                                   MSHMessageType) ||
      hl7_segment_set_field_string(ack_msh, hl7_generate_control_id(),
                                   separators, MSHMessageControlID) ||
      hl7_segment_set_field_string(ack_msh, "P", separators, MSHProcessingID) ||
#if HL7_VERSION == 260
      hl7_segment_set_field_string(ack_msh, "2.6", separators, MSHVersionID)
#elif HL7_VERSION == 250
      hl7_segment_set_field_string(ack_msh, "2.5", separators, MSHVersionID)
#elif HL7_VERSION == 240
      hl7_segment_set_field_string(ack_msh, "2.4", separators, MSHVersionID)
#endif
  ) {
    hl7_segment_destruct(ack_msh);
    return NULL;
  }

  free(message_type_str);

  // msa segment
  Segment *ack_msa = hl7_segment_create();
  if (!ack_msa) {
    hl7_segment_destruct(ack_msh);
    return NULL;
  }

  hl7_segment_set_type(ack_msa, SegmentTypeMSA);
  int status;
  if (success)
    status = hl7_segment_set_field_string(ack_msa, "CA", separators,
                                          MSAAcknowledgmentCode);
  else
    status = hl7_segment_set_field_string(ack_msa, "CR", separators,
                                          MSAAcknowledgmentCode);
  if (status || hl7_segment_set_field_string(ack_msa, hl7_generate_control_id(),
                                             separators, MSAMessageControlID)) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);

    return NULL;
  }

  // message
  Message *ack = hl7_message_create();
  if (!ack) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);
  }

  if (hl7_message_add_segment(ack, ack_msh) ||
      hl7_message_add_segment(ack, ack_msa)) {
    hl7_segment_destruct(ack_msh);
    hl7_segment_destruct(ack_msa);
    hl7_message_destruct(ack);

    return NULL;
  }

  char *error_message = hl7_message_is_valid(ack);
  if (!error_message) {
    last_error_display(LOG_ERROR, "couldn't validate acknowledgment");
  } else if (strcmp(error_message, "") != 0) {
    log_warn("acknowledgment doesn't follow HL7 %d standard (this can be due "
             "to original message not following standard):\n%s",
             HL7_VERSION, error_message);
  } else {
    log_info("acknowledgment follows HL7 %s standard", HL7_VERSION);
  }

  if (error_message)
    free(error_message);

  return ack;
}

#endif

int application_layer_init(ApplicationConfiguration *init_config,
                           PubSub *init_transport2application,
                           PubSub *init_application2transport,
                           PubSub *init_application2storing) {
  config = init_config;
  transport2application = init_transport2application;
  application2transport = init_application2transport;
  application2storing = init_application2storing;

  bb = bb_create(buffer, BUFFER_SIZE);
  if (!bb) {
    last_error_display(LOG_ERROR, "can't create binary buffer");
    return 1;
  }

  if (add_subscriber(transport2application, application_layer_process_message,
                     NULL)) {

    last_error_display(LOG_ERROR, "can't subscribe");
    return 1;
  }

  return 0;
}

static int send_acknowledgment(const Message *message, const int succes) {
  int status = 0;
  BinaryBuffer *bb_tmp;
  Message *ack;
  unsigned char *bytes;
  char *ack_str;
  BinaryBufferDocument **docs;

  if (succes) {
    log_start(LOG_DEBUG, "constructing acknowledgment for a valid message...");
  } else {
    log_start(LOG_DEBUG,
              "constructing acknowledgment for an invalid message...");
  }

  // build acknowledgment message
  ack = generate_ack(message, succes, config->sending_application,
                     config->sending_facility);
  if (!ack) {
    status = 1;
    goto send_acknowledgment_clean;
  }
  log_end(LOG_DEBUG, "done.");

  // convert to string
  ack_str = hl7_message_to_string(ack);
  if (!ack_str) {
    status = 1;
    goto send_acknowledgment_clean;
  }

  log_debug("sending acknowledgment : \n%s", ack_str);

  // create binary buffer
  size_t str_len = strlen(ack_str);
  size_t bytes_count = str_len + 4;
  LOG_CHECK_MALLOC(bytes, malloc(bytes_count));
  LOG_CHECK_MALLOC(bb_tmp, bb_from_string(ack_str, bytes, bytes_count));

  // send to transport layer
  docs = bb_to_documents_try(bb_tmp);
  if (!docs[0] || docs[1]) {
    log_error("one document should have been created");
    status = 1;
    goto send_acknowledgment_clean;
  }
  size_t length;
  const unsigned char *buffer_bytes =
      bb_document_to_bytes(bb_tmp, docs[0], &length);
  ApplicationToTransportData data = {.bytes = buffer_bytes, .length = length};
  publish_message(application2transport, &data);

send_acknowledgment_clean:
  if (bb_tmp)
    bb_destruct(bb_tmp);
  if (bytes)
    free(bytes);
  if (ack_str)
    free(ack_str);
  if (ack)
    hl7_message_destruct(ack);
  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++)
      free(*ptr);
    free(docs);
  }

  return status;
}

static void store_binary(const BinaryBufferDocument *doc, const char *ts) {
  size_t length;
  const unsigned char *data = bb_document_to_bytes(bb, doc, &length);
  BinaryFileData data_bin = {.bytes = data, .length = length};
  ApplicationToStoringData storing_data_bin = {
      .folder = ts, .file_data = &data_bin, .file_type = FT_BINARY, .name = ts};
  publish_message(application2storing, &storing_data_bin);
}

static void store_textual(const BinaryBufferDocument *doc, const char *ts) {
  const char *str = bb_document_to_string(bb, doc);
  TextualFileData data_text = {.message = str};
  ApplicationToStoringData storing_data_txt = {.folder = ts,
                                               .file_data = &data_text,
                                               .file_type = FT_TEXTUAL,
                                               .name = ts};
  publish_message(application2storing, &storing_data_txt);
}

void process_message(const BinaryBufferDocument *doc) {
  Message *message = NULL;
  const char *ts = hl7_generate_date_time();

  // binary storage
  store_binary(doc, ts);

  // textual storage
  store_textual(doc, ts);

  const char *str = bb_document_to_string(bb, doc);

  // parsing
  log_start(LOG_DEBUG, "parsing message...");
  message = hl7_message_from_string(str);
  if (!message) {
    last_error_display(LOG_WARN, "message can't be parsed");
    goto process_message_clean;
  }
  log_end(LOG_DEBUG, "done.");

  // validation
  char *error_message = hl7_message_is_valid(message);
  if (!error_message) {
    last_error_display(LOG_ERROR, "couldn't run validation on message");
  } else if (strcmp(error_message, "") == 0) {
    log_info("message follows HL7 %s standard", HL7_VERSION);
  } else {
    log_warn("message doesn't follow HL7 %d standard :\n%s", HL7_VERSION,
             error_message);
  }
  if (error_message)
    free(error_message);

  // acknowledgment
  if (config->delay_before_ack != 0) {
    log_start(LOG_DEBUG, "waiting %ld seconds before sending acknowledgment...",
              config->delay_before_ack);
    sleep(config->delay_before_ack);
    log_end(LOG_DEBUG, "done.");
  }
  send_acknowledgment(message, true);

process_message_clean:
  hl7_message_destruct(message);
  bb_clean(bb);
}

void application_layer_process_message(void *void_context,
                                       const void *void_data) {
  (void)void_context;
  const TransportToApplicationData *data =
      (TransportToApplicationData *)void_data;

  // invalid length
  if (data->length == 0) {
    log_warn("received zero-length message");
    return;
  }

  bb_append(bb, data->bytes, data->length);
  BinaryBufferDocument **docs = bb_to_documents_try(bb);

  if (docs) {
    for (BinaryBufferDocument **ptr = docs; *ptr; ptr++) {
      process_message(*ptr);
      free(*ptr);
    }

    free(docs);
  } else
    last_error_display(LOG_WARN, "can't read binary buffer");
}

void application_layer_end() { bb_destruct(bb); }
