#include "cx.h"

#if HL7_VERSION == 240

const SubFieldSpec CX_SPEC[8] = {
    {FieldTypeST, 0, REQUIRED, TABLE_NONE},
    {FieldTypeST, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 0, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0061},
    {<PERSON>T<PERSON>HD, 0, OP<PERSON>ON<PERSON>, TableType0363},
    {FieldTypeID, 0, OPTIONAL, TableType0203},
    {FieldTypeHD, 0, OPTION<PERSON>, TABLE_NONE},
    {FieldTypeDT, 0, OPTIONAL, TABLE_NONE},
    {FieldTypeDT, 0, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 250

const SubFieldSpec CX_SPEC[10] = {
    {FieldTypeST, 15, REQUIRED, TABLE_NONE},
    {FieldTypeST, 1, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 3, O<PERSON><PERSON><PERSON><PERSON>, TableType0061},
    {FieldTypeHD, 227, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0363},
    {FieldTypeID, 5, <PERSON><PERSON><PERSON><PERSON><PERSON>, TableType0203},
    {FieldTypeHD, 227, <PERSON><PERSON><PERSON><PERSON><PERSON>, TABLE_NONE},
    {FieldTypeDT, 8, OPTIONAL, TABLE_NONE},
    {FieldTypeDT, 8, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
};

#elif HL7_VERSION == 260

const SubFieldSpec CX_SPEC[10] = {
    {FieldTypeST, 15, REQUIRED, TABLE_NONE},
    {FieldTypeST, 4, OPTIONAL, TABLE_NONE},
    {FieldTypeID, 3, OPTIONAL, TableType0061},
    {FieldTypeHD, 227, OPTIONAL, TableType0363},
    {FieldTypeID, 5, OPTIONAL, TableType0203},
    {FieldTypeHD, 227, OPTIONAL, TABLE_NONE},
    {FieldTypeDT, 8, OPTIONAL, TABLE_NONE},
    {FieldTypeDT, 8, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
    {FieldTypeCWE, 705, OPTIONAL, TABLE_NONE},
};

#endif
