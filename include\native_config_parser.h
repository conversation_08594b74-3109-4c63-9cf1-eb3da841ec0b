/**
 * @file native_config_parser.h
 * @brief Native configuration file parser to replace libconfuse.
 *
 * This parser handles configuration files with sections in the format:
 * section_name {
 *   key = value
 *   # comments
 * }
 *
 * Supports:
 * - String values (with quotes): key = "value"
 * - Integer values: key = 123
 * - Boolean values: key = true/false
 * - Comments starting with #
 * - Nested sections
 *
 * Optimized for Linux RedHat with:
 * - Cache-aligned memory allocation
 * - Branch prediction hints
 * - Efficient I/O buffering
 * - bzero usage for memory clearing
 */

#pragma once

#include <stdbool.h>
#include <stddef.h>

#define MAX_CONFIG_LINE_LENGTH 1024
#define MAX_CONFIG_KEY_LENGTH 128
#define MAX_CONFIG_VALUE_LENGTH 512
#define MAX_CONFIG_SECTION_LENGTH 64

/**
 * @brief Configuration value types
 */
typedef enum {
    CONFIG_TYPE_STRING,
    CONFIG_TYPE_INT,
    CONFIG_TYPE_BOOL
} ConfigValueType;

/**
 * @brief Configuration value structure
 */
typedef struct {
    char key[MAX_CONFIG_KEY_LENGTH];
    ConfigValueType type;
    union {
        char string_value[MAX_CONFIG_VALUE_LENGTH];
        long int_value;
        bool bool_value;
    } value;
} ConfigValue;

/**
 * @brief Configuration section structure
 */
typedef struct {
    char name[MAX_CONFIG_SECTION_LENGTH];
    ConfigValue *values;
    size_t value_count;
    size_t value_capacity;
} ConfigSection;

/**
 * @brief Main configuration parser structure
 */
typedef struct {
    ConfigSection *sections;
    size_t section_count;
    size_t section_capacity;
} ConfigParser;

/**
 * @brief Initialize a configuration parser
 * @param parser Pointer to the parser structure
 * @return 0 on success, non-zero on error
 */
int config_parser_init(ConfigParser *parser);

/**
 * @brief Parse a configuration file
 * @param parser Pointer to the parser structure
 * @param filename Path to the configuration file
 * @return 0 on success, non-zero on error
 */
int config_parser_parse_file(ConfigParser *parser, const char *filename);

/**
 * @brief Get a string value from a section
 * @param parser Pointer to the parser structure
 * @param section_name Name of the section
 * @param key Key name
 * @param default_value Default value if key not found
 * @return Pointer to the string value or default_value
 */
const char *config_parser_get_string(const ConfigParser *parser, 
                                     const char *section_name, 
                                     const char *key, 
                                     const char *default_value);

/**
 * @brief Get an integer value from a section
 * @param parser Pointer to the parser structure
 * @param section_name Name of the section
 * @param key Key name
 * @param default_value Default value if key not found
 * @return Integer value or default_value
 */
long config_parser_get_int(const ConfigParser *parser, 
                          const char *section_name, 
                          const char *key, 
                          long default_value);

/**
 * @brief Get a boolean value from a section
 * @param parser Pointer to the parser structure
 * @param section_name Name of the section
 * @param key Key name
 * @param default_value Default value if key not found
 * @return Boolean value or default_value
 */
bool config_parser_get_bool(const ConfigParser *parser, 
                           const char *section_name, 
                           const char *key, 
                           bool default_value);

/**
 * @brief Check if a key exists in a section
 * @param parser Pointer to the parser structure
 * @param section_name Name of the section
 * @param key Key name
 * @return true if key exists, false otherwise
 */
bool config_parser_has_key(const ConfigParser *parser, 
                          const char *section_name, 
                          const char *key);

/**
 * @brief Free all memory allocated by the parser
 * @param parser Pointer to the parser structure
 */
void config_parser_cleanup(ConfigParser *parser);
