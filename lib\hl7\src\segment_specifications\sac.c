#include "sac.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec SAC_Spec[44] = {
    {"external accession identifier", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"accession identifier", FieldTypeEI, 80, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"container identifier", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"primary", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"equipment container identifier", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen source", FieldTypeSPS, 300, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"registration date/time", FieldTypeTS, 26, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"container status", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0370},
    {"carrier type", FieldTypeCE, 250, OP<PERSON>ON<PERSON>, NOT_REPEATABLE, TableType0378},
    {"carrier identifier", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"position in carrier", FieldTypeNA, 80, OPTIONAL, NOT_REPEATABLE, TableType0379},
    {"tray type - SAC", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"tray identifier", FieldTypeEI, 80, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"position in tray", FieldTypeNA, 80, OPTIONAL, REPEATABLE_INFINITE, TABLE_NONE},
    {"location", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"container height", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"container diameter", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"barrier delta", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"bottom delta", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"container height/diameter/delta units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"container volume", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"available volume", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"initial specimen volume", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"volume units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0380},
    {"separator type", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0381},
    {"cap type", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0371},
    {"additive", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"specimen component", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"dilution factor", FieldTypeSN, 20, OPTIONAL, NOT_REPEATABLE, TableType0373},
    {"treatment", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"temperature", FieldTypeSN, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"hemolysis index", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"hemolysis index units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"lipemia index", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"lipemia index units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"icterus index", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"icterus index units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"fibrin index", FieldTypeNM, 20, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"fibrin index units", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"system induced contaminants", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0374},
    {"drug interference", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0382},
    {"artificial blood", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0375},
    {"special handling considerations", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0376},
    {"other environmental factor", FieldTypeCE, 250, OPTIONAL, REPEATABLE_INFINITE, TableType0377},
};
// clang-format on

#endif