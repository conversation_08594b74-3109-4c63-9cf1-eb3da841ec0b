#include "poct_config.h"

#include "log.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024

void application_configuration_init(ApplicationConfiguration *config) {
  (void)config;
  config->response_timeout_ms = 0;
}

#define FREE_AND_SET_NULL(attribute)                                           \
  if (attribute) {                                                             \
    free(attribute);                                                           \
    attribute = NULL;                                                          \
  }

void application_configuration_destruct(ApplicationConfiguration *config) {
  (void)config;

  if (!config)
    return;
}

char *
application_configuration_to_string(const ApplicationConfiguration *config) {
  (void)config;

  // Allocate a buffer to store the resulting string
  char *result;
  LOG_CHECK_MALLOC(result, malloc(BUFFER_SIZE));

  // Initialize the string
  snprintf(result, BUFFER_SIZE,
           "Configuration:\n"
           " %s: %zu\n",
           CONFIG_RESPONSE_TIMEOUT, config->response_timeout_ms);

  return result;
}
