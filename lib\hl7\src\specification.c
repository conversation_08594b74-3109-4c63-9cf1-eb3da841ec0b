#include "specification.h"

#include "log.h"
#include <assert.h>
#include <message.h>
#include <stdlib.h>
#include <string.h>

static void *error_clone(const void *error_void) {
  HL7ParsingError *error = (HL7ParsingError *)error_void;
  HL7ParsingError *clone_error;
  LOG_CHECK_MALLOC(clone_error, malloc(sizeof(HL7ParsingError)));

  clone_error->message = strdup(error->message);
  clone_error->position = hl7_position_clone(error->position);

  return clone_error;
}

static void error_destruct(void *error_void) {
  if (!error_void)
    return;

  HL7ParsingError *error = (HL7ParsingError *)error_void;

  if (error->message)
    free(error->message);

  if (error->position)
    free(error->position);

  free(error);
}

HL7ParsingErrorsHelper *hl7_errors_helper_create(const Message *message) {
  HL7ParsingErrorsHelper *helper;
  LOG_CHECK_MALLOC(helper, malloc(sizeof(HL7ParsingErrorsHelper)));

  helper->current_position.segment_spec = NULL;
  helper->current_position.segment_index = 0;
  helper->current_position.segment_type = SegmentTypeUnknown;
  helper->current_position.field_index = 0;
  helper->current_position.field_type = FieldTypeUnknown;
  helper->current_position.character_index = 0;
  helper->message = message;

  helper->errors = dl_create(error_clone, error_destruct);
  if (!helper->errors) {
    free(helper);
    return NULL;
  }

  return helper;
}

HL7ParsingErrorPosition *
hl7_position_clone(const HL7ParsingErrorPosition *position) {
  HL7ParsingErrorPosition *new_position =
      malloc(sizeof(HL7ParsingErrorPosition));
  new_position->segment_spec = position->segment_spec;
  new_position->segment_index = position->segment_index;
  new_position->segment_type = position->segment_type;
  new_position->field_index = position->field_index;
  new_position->field_type = position->field_type;
  new_position->character_index = position->character_index;

  return new_position;
}

int hl7_errors_helper_add(HL7ParsingErrorsHelper *helper, const char *format,
                          ...) {
  // Allocate a buffer to store the formatted error message
  char *error_message;
  LOG_CHECK_MALLOC(error_message,
                   malloc(1024)); // You can dynamically adjust this size

  va_list args;
  va_start(args, format);
  vsnprintf(error_message, 1024, format, args); // Format the error message
  va_end(args);

  // Create a new error and copy the formatted message
  HL7ParsingError *error;
  LOG_CHECK_MALLOC(error, malloc(sizeof(HL7ParsingError)));

  error->message = error_message; // Assign the formatted message
  error->position = hl7_position_clone(&helper->current_position);

  return dl_append_by_value(helper->errors, error);
}

static char *error_field_str(const HL7ParsingError *error) {
  const HL7ParsingErrorPosition *pos = error->position;
  const char *field_name = (*pos->segment_spec)[pos->field_index].name;

  size_t length = strlen(field_name) +
                  strlen(hl7_field_type2str(pos->field_type)) +
                  strlen(error->message) + 50; // room for formatting
  char *str;
  LOG_CHECK_MALLOC(str, malloc(length * sizeof(char)));

  snprintf(str, length, "field \"%s\" (type %s) (index %zu) : %s", field_name,
           hl7_field_type2str(pos->field_type), pos->field_index,
           error->message);

  return str;
}

static char *error_segment_str(const Segment *segment,
                               const Separators *separators,
                               const HL7ParsingError **errors,
                               size_t error_count, size_t segment_index) {
  // Get the segment string
  char *segment_str = hl7_segment_to_string(segment, separators);
  if (!segment_str)
    return NULL;

  // Field strings
  size_t segment_len = strlen(segment_str);
  size_t length =
      30 + 2 * segment_len; // first line, segment string, and numbers line
  char **substrs;
  LOG_CHECK_MALLOC(substrs, malloc(error_count * sizeof(char *)));

  for (size_t i = 0; i < error_count; i++) {
    const HL7ParsingError *error = errors[i];
    char *substr;

    if (error->position->field_type == FieldTypeUnknown) {
      // Segment error
      substr = strdup(error->message);
    } else {
      // Field error
      substr = error_field_str(error);
    }

    length += 5 + strlen(substr) + 2; // error index + substr + newline
    substrs[i] = substr;
  }

  // Create final string
  char *str;
  LOG_CHECK_MALLOC(str, malloc(length * sizeof(char)));

  snprintf(str, length, "segment %s (index %zu)\n",
           hl7_segment_type2str(segment->type), segment_index);
  strcat(str, segment_str);
  strcat(str, "\n");

  // Numbers line
  char *numbers_line;
  LOG_CHECK_MALLOC(numbers_line, malloc((segment_len + 2) * sizeof(char)));

  memset(numbers_line, ' ', segment_len);
  numbers_line[segment_len] = '\n';
  numbers_line[segment_len + 1] = '\0';

  for (size_t i = 0; i < error_count; i++) {
    static char n_buffer[2];
    const HL7ParsingError *error = errors[i];
    size_t char_index = error->position->character_index;
    assert(char_index <= segment_len);
    snprintf(n_buffer, 2, "%zu", (i + 1) % 10);
    numbers_line[char_index] = n_buffer[0];
  }
  strcat(str, numbers_line);
  free(numbers_line);

  // Append errors
  for (size_t i = 0; i < error_count; i++) {
    snprintf(str + strlen(str), length - strlen(str), "%zu) %s\n", i + 1,
             substrs[i]);
    free(substrs[i]);
  }

  free(substrs);
  free(segment_str);

  return str;
}

char *hl7_errors2str(const HL7ParsingErrorsHelper *helper) {
  size_t errors_count = dl_length(helper->errors);
  if (errors_count == 0)
    return strdup("");

  size_t length = 0;
  char **substrs;
  LOG_CHECK_MALLOC(substrs, malloc((errors_count + 1) * sizeof(char *)));

  size_t substrs_count = 0;

  // Message errors
  for (size_t i = 0; i < errors_count; i++) {
    const HL7ParsingError *error = dl_get(helper->errors, i);
    if (error->position->segment_type != SegmentTypeUnknown)
      continue;

    char *substr = strdup(error->message);
    length += strlen(substr) + 2; // line breaks
    substrs[substrs_count++] = substr;
  }

  // Find maximum segment index
  size_t segment_max_index = 0;
  for (size_t i = 0; i < errors_count; i++) {
    const HL7ParsingError *error = dl_get(helper->errors, i);
    if (error->position->segment_index > segment_max_index)
      segment_max_index = error->position->segment_index;
  }

  // Segment errors
  const HL7ParsingError **segment_errors =
      malloc(errors_count * sizeof(HL7ParsingError *));
  for (size_t i = 0; i <= segment_max_index; i++) {
    size_t segment_errors_count = 0;
    for (size_t j = 0; j < errors_count; j++) {
      const HL7ParsingError *error = dl_get(helper->errors, j);
      if (error->position->segment_type == SegmentTypeUnknown)
        continue;

      if (error->position->segment_index == i)
        segment_errors[segment_errors_count++] = error;
    }

    if (segment_errors_count == 0)
      continue;

    const Segment *segment =
        hl7_message_get_segment_by_index(helper->message, i);
    char *substr = error_segment_str(segment, &helper->message->separators,
                                     segment_errors, segment_errors_count, i);
    length += strlen(substr) + 2; // line breaks
    substrs[substrs_count++] = substr;
  }

  substrs[substrs_count] = NULL;

  // Create final string
  char *str;
  LOG_CHECK_MALLOC(str, malloc((length + 1) * sizeof(char)));

  str[0] = '\0';
  for (char **substr_ptr = substrs; *substr_ptr; substr_ptr++) {
    strcat(str, *substr_ptr);
    strcat(str, "\n\n");
    free(*substr_ptr);
  }

  free(substrs);
  free(segment_errors);

  return str;
}

void hl7_errors_helper_destruct(HL7ParsingErrorsHelper *helper) {
  if (!helper)
    return;

  if (helper->errors) {
    dl_destruct(helper->errors);
    free(helper->errors);
  }

  free(helper);
}
