#include "sac.h"

#if HL7_VERSION == 240

// clang-format off
const FieldInSegmentSpec SID_Spec[4] = {
    {"application/method identifier", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"substance lot number", FieldTypeST, 20, OP<PERSON><PERSON><PERSON>, NOT_REPEATABLE, TABLE_NONE},
    {"substance container identifier", FieldTypeST, 200, OPTIONAL, NOT_REPEATABLE, TABLE_NONE},
    {"substance manufacturer identifier", FieldTypeCE, 250, OPTIONAL, NOT_REPEATABLE, TableType0385},
};
// clang-format on

#endif