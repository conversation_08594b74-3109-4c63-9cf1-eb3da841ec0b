/*
 * Copyright (c) 2020 rxi
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 */

#include "log.h"

#include <string.h>
#include <time.h>

#define MAX_CALLBACKS 32

typedef struct {
  log_LogFn fn;
  void *udata;
  int level;
} Callback;

static struct {
  void *udata;
  log_LockFn lock;
  int level;
  bool quiet;
  bool next_is_start;      // ADDED
  bool next_is_end;        // ADDED
  bool next_is_space;      // ADDED
  bool last_was_start;     // ADDED
  bool next_is_last_error; // ADDED
  bool last_error_is_set;  // ADDED
  Callback callbacks[MAX_CALLBACKS];
} L;

static const char *level_strings[] = {"TRACE", "DEBUG", "INFO",
                                      "WARN",  "ERROR", "FATAL"};

#ifdef LOG_USE_COLOR
static const char *level_colors[] = {"\x1b[94m", "\x1b[36m", "\x1b[32m",
                                     "\x1b[33m", "\x1b[31m", "\x1b[35m"};
#endif

static void stdout_callback(log_Event *ev) {
  char buf[16];
  buf[strftime(buf, sizeof(buf), "%H:%M:%S", ev->time)] = '\0';
  if (L.last_was_start && !L.next_is_end)   // ADDED
    fprintf(ev->udata, "\n");               // ADDED
  if (L.next_is_last_error)                 // ADDED
    fprintf(ev->udata, " ==> ");            // ADDED
  if (!L.next_is_end && !L.next_is_space) { // ADDED
#ifdef LOG_USE_COLOR
    fprintf(ev->udata, "%s %s%-5s\x1b[0m \x1b[90m%-60s:%-4d:\x1b[0m ", buf,
            level_colors[ev->level], level_strings[ev->level], ev->file,
            ev->line);
#else
    fprintf(ev->udata, "%s %-5s %-60s:%-4d: ", buf, level_strings[ev->level],
            ev->file, ev->line);
#endif
  } // ADDED
  vfprintf(ev->udata, ev->fmt, ev->ap);
  if (!L.next_is_start) // ADDED
    fprintf(ev->udata, "\n");
  fflush(ev->udata);
}

static void file_callback(log_Event *ev) {
  char buf[64];
  buf[strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", ev->time)] = '\0';
  if (L.last_was_start && !L.next_is_end)   // ADDED
    fprintf(ev->udata, "\n");               // ADDED
  if (!L.next_is_end && !L.next_is_space) { // ADDED
    fprintf(ev->udata, "%s %-5s %-70s:%-4d: ", buf, level_strings[ev->level],
            ev->file, ev->line);
  } // ADDED
  vfprintf(ev->udata, ev->fmt, ev->ap);
  if (!L.next_is_start) // ADDED
    fprintf(ev->udata, "\n");
  fflush(ev->udata);
}

static void lock(void) {
  if (L.lock)
    L.lock(true, L.udata);
}

static void unlock(void) {
  if (L.lock)
    L.lock(false, L.udata);
}

const char *log_level_string(int level) { return level_strings[level]; }

void log_set_lock(log_LockFn fn, void *udata) {
  L.lock = fn;
  L.udata = udata;
}

void log_set_level(int level) { L.level = level; }

void log_set_quiet(bool enable) { L.quiet = enable; }

int log_add_callback(log_LogFn fn, void *udata, int level) {
  for (int i = 0; i < MAX_CALLBACKS; i++) {
    if (!L.callbacks[i].fn) {
      L.callbacks[i] = (Callback){fn, udata, level};
      return 0;
    }
  }
  return -1;
}

int log_add_fp(FILE *fp, int level) {
  return log_add_callback(file_callback, fp, level);
}

static void init_event(log_Event *ev, void *udata) {
  if (!ev->time) {
    time_t t = time(NULL);
    ev->time = localtime(&t);
  }
  ev->udata = udata;
}

void update_state() {                 // ADDED
  L.last_was_start = L.next_is_start; // ADDED
  L.next_is_start = false;            // ADDED
  L.next_is_end = false;              // ADDED
  L.next_is_space = false;            // ADDED
  L.next_is_last_error = false;       // ADDED
} // ADDED

void log_log(int level, const char *file, int line, const char *fmt, ...) {
  log_Event ev = {
      .fmt = fmt,
      .file = file,
      .line = line,
      .level = level,
  };

  lock();

  if (!L.quiet && level >= L.level) {
    init_event(&ev, stderr);
    va_start(ev.ap, fmt);
    stdout_callback(&ev);
    va_end(ev.ap);
  }

  for (int i = 0; i < MAX_CALLBACKS && L.callbacks[i].fn; i++) {
    Callback *cb = &L.callbacks[i];
    if (level >= cb->level) {
      init_event(&ev, cb->udata);
      va_start(ev.ap, fmt);
      cb->fn(&ev);
      va_end(ev.ap);
    }
  }

  update_state(); // ADDED

  unlock();
}

// ADDED CODE
// Everything below was not originally part of the library

void help(const char *level_str) {
  log_error("Unknown log level : %s\n The available log levels are : trace, "
            "debug, info, warn, error, fatal",
            level_str);
}

int string_to_log_level(const char *level_str) {
  if (strcasecmp(level_str, "trace") == 0)
    return LOG_TRACE;
  else if (strcasecmp(level_str, "debug") == 0)
    return LOG_DEBUG;
  else if (strcasecmp(level_str, "info") == 0)
    return LOG_INFO;
  else if (strcasecmp(level_str, "warn") == 0)
    return LOG_WARN;
  else if (strcasecmp(level_str, "error") == 0)
    return LOG_ERROR;
  else if (strcasecmp(level_str, "fatal") == 0)
    return LOG_FATAL;
  else {
    help(level_str);
    return 1;
  }
}

#define MAX_ERROR_MSG_LEN 4096

static log_Event last_error;
static char last_error_msg[MAX_ERROR_MSG_LEN] = {
    0}; // To store formatted error message

void last_error_set_fn(const char *file, int line, const char *fmt, ...) {
  // Set up the last_error_event
  last_error.file = file;
  last_error.line = line;
  last_error.level =
      LOG_ERROR; // will change when `last_error_display_fn` gets called
  time_t t = time(NULL);
  last_error.time = localtime(&t);

  // Format the error message
  va_list args;
  va_start(args, fmt);
  vsnprintf(last_error_msg, sizeof(last_error_msg), fmt, args);
  va_end(args);

  // Store the formatted message in last_error_event
  last_error.fmt = last_error_msg;
  L.last_error_is_set = true;
}

void last_error_display_fn(int level) {
  if (!L.last_error_is_set)
    return;

  last_error.level = level;
  L.next_is_last_error = true;
  log_log(last_error.level, last_error.file, last_error.line, "%s",
          last_error.fmt);
  L.last_error_is_set = false;
}

void log_next_is_start() { L.next_is_start = true; }

void log_next_is_end() {
  if (!L.last_was_start)
    return;

  L.next_is_end = true;
}

void before_log_space() {
  L.next_is_space = true;
  L.next_is_end = false;
  L.next_is_start = false;
}